from flask import Blueprint, jsonify, request
import openai
import json
import random

ai_bp = Blueprint('ai', __name__)

# Sample viral post templates for inspiration
VIRAL_POST_TEMPLATES = [
    {
        "hook": "Here's what I learned after {number} {time_period} of {activity}:",
        "format": "lesson_learned",
        "engagement_score": 8.5
    },
    {
        "hook": "Unpopular opinion:",
        "format": "controversial_take",
        "engagement_score": 9.2
    },
    {
        "hook": "I made a {mistake/decision} that cost me {amount/time}. Here's what I learned:",
        "format": "failure_story",
        "engagement_score": 8.8
    },
    {
        "hook": "3 things I wish I knew before {starting/doing} {activity}:",
        "format": "advice_list",
        "engagement_score": 7.9
    },
    {
        "hook": "This {simple/small} change increased my {metric} by {percentage}%:",
        "format": "success_story",
        "engagement_score": 8.3
    }
]

@ai_bp.route('/ai/generate-content', methods=['POST'])
def generate_content():
    data = request.json
    topic = data.get('topic', '')
    tone = data.get('tone', 'professional')
    format_type = data.get('format', 'general')
    
    try:
        # Create a prompt for content generation
        prompt = f"""
        Generate a LinkedIn post about {topic} with a {tone} tone.
        Format: {format_type}
        
        The post should be:
        - Engaging and professional
        - 150-300 words
        - Include relevant hashtags
        - Have a clear call-to-action
        
        Make it authentic and valuable to the LinkedIn audience.
        """
        
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a LinkedIn content expert who creates engaging, professional posts that drive engagement."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=500,
            temperature=0.7
        )
        
        generated_content = response.choices[0].message.content.strip()
        
        return jsonify({
            'content': generated_content,
            'topic': topic,
            'tone': tone,
            'format': format_type
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/ai/improve-content', methods=['POST'])
def improve_content():
    data = request.json
    content = data.get('content', '')
    improvement_type = data.get('type', 'general')  # general, engagement, clarity, tone
    
    try:
        improvement_prompts = {
            'engagement': 'Make this LinkedIn post more engaging and likely to get likes, comments, and shares',
            'clarity': 'Improve the clarity and readability of this LinkedIn post',
            'tone': 'Adjust the tone of this LinkedIn post to be more professional yet approachable',
            'general': 'Improve this LinkedIn post overall for better performance'
        }
        
        prompt = f"""
        {improvement_prompts.get(improvement_type, improvement_prompts['general'])}:
        
        Original post:
        {content}
        
        Provide an improved version that maintains the core message but enhances the specified aspect.
        """
        
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a LinkedIn content optimization expert."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=500,
            temperature=0.7
        )
        
        improved_content = response.choices[0].message.content.strip()
        
        return jsonify({
            'original_content': content,
            'improved_content': improved_content,
            'improvement_type': improvement_type
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/ai/viral-inspiration', methods=['GET'])
def get_viral_inspiration():
    topic = request.args.get('topic', '')
    count = int(request.args.get('count', 5))
    
    # Filter templates by topic relevance (simplified)
    relevant_templates = VIRAL_POST_TEMPLATES.copy()
    
    # Randomly select templates
    selected_templates = random.sample(relevant_templates, min(count, len(relevant_templates)))
    
    return jsonify({
        'templates': selected_templates,
        'topic': topic,
        'count': len(selected_templates)
    })

@ai_bp.route('/ai/hashtag-suggestions', methods=['POST'])
def suggest_hashtags():
    data = request.json
    content = data.get('content', '')
    industry = data.get('industry', '')
    
    try:
        prompt = f"""
        Suggest 5-10 relevant hashtags for this LinkedIn post:
        
        Content: {content}
        Industry: {industry}
        
        Provide hashtags that are:
        - Relevant to the content
        - Popular but not oversaturated
        - Mix of broad and niche tags
        - Professional and appropriate for LinkedIn
        
        Return only the hashtags, separated by spaces.
        """
        
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a LinkedIn hashtag expert."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=200,
            temperature=0.5
        )
        
        hashtags = response.choices[0].message.content.strip().split()
        
        return jsonify({
            'hashtags': hashtags,
            'content_preview': content[:100] + '...' if len(content) > 100 else content
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/ai/engagement-prediction', methods=['POST'])
def predict_engagement():
    data = request.json
    content = data.get('content', '')
    
    # Simplified engagement prediction based on content characteristics
    score = 5.0  # Base score
    
    # Analyze content characteristics
    word_count = len(content.split())
    has_question = '?' in content
    has_hashtags = '#' in content
    has_call_to_action = any(phrase in content.lower() for phrase in ['what do you think', 'share your', 'comment below', 'let me know'])
    
    # Adjust score based on characteristics
    if 50 <= word_count <= 200:
        score += 1.5
    elif word_count > 300:
        score -= 1.0
    
    if has_question:
        score += 1.0
    if has_hashtags:
        score += 0.5
    if has_call_to_action:
        score += 1.5
    
    # Cap the score
    score = min(10.0, max(1.0, score))
    
    # Generate recommendations
    recommendations = []
    if word_count > 250:
        recommendations.append("Consider shortening the post for better engagement")
    if not has_question:
        recommendations.append("Add a question to encourage comments")
    if not has_call_to_action:
        recommendations.append("Include a call-to-action to boost engagement")
    if not has_hashtags:
        recommendations.append("Add relevant hashtags to increase discoverability")
    
    return jsonify({
        'engagement_score': round(score, 1),
        'predicted_range': f"{int(score*10)}-{int(score*15)} interactions",
        'recommendations': recommendations,
        'analysis': {
            'word_count': word_count,
            'has_question': has_question,
            'has_hashtags': has_hashtags,
            'has_call_to_action': has_call_to_action
        }
    })

