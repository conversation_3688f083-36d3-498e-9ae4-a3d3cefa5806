from flask import Blueprint, jsonify, request
from datetime import datetime
from src.services.linkedin_automation import LinkedInAutomation, LinkedInProfile
from src.models.campaign import Campaign, db
from src.models.connection import Connection

automation_bp = Blueprint('automation', __name__)

# Initialize LinkedIn automation service
linkedin_service = LinkedInAutomation()

@automation_bp.route('/automation/search-profiles', methods=['POST'])
def search_profiles():
    """Search for LinkedIn profiles based on criteria"""
    data = request.json
    search_criteria = data.get('criteria', {})
    
    try:
        profiles = linkedin_service.search_profiles(search_criteria)
        
        # Convert profiles to dict format
        profile_results = []
        for profile in profiles:
            profile_results.append({
                'profile_url': profile.profile_url,
                'name': profile.name,
                'title': profile.title,
                'company': profile.company,
                'location': profile.location,
                'mutual_connections': profile.mutual_connections,
                'profile_views': profile.profile_views
            })
        
        return jsonify({
            'profiles': profile_results,
            'total_found': len(profile_results),
            'search_criteria': search_criteria
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/send-connection-request', methods=['POST'])
def send_connection_request():
    """Send a connection request to a LinkedIn profile"""
    data = request.json
    profile_data = data.get('profile')
    message = data.get('message', '')
    user_id = data.get('user_id')
    
    try:
        # Create LinkedInProfile object
        profile = LinkedInProfile(
            profile_url=profile_data['profile_url'],
            name=profile_data['name'],
            title=profile_data['title'],
            company=profile_data['company'],
            location=profile_data['location']
        )
        
        # Send connection request
        result = linkedin_service.send_connection_request(profile, message)
        
        # If successful, save to database
        if result['success'] and user_id:
            connection = Connection(
                user_id=user_id,
                linkedin_profile_url=profile.profile_url,
                name=profile.name,
                title=profile.title,
                company=profile.company,
                location=profile.location,
                connection_status='pending',
                notes=f"Connection request sent with message: {message}"
            )
            db.session.add(connection)
            db.session.commit()
            
            result['connection_id'] = connection.id
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/send-message', methods=['POST'])
def send_message():
    """Send a direct message to a LinkedIn connection"""
    data = request.json
    profile_data = data.get('profile')
    message = data.get('message')
    
    try:
        # Create LinkedInProfile object
        profile = LinkedInProfile(
            profile_url=profile_data['profile_url'],
            name=profile_data['name'],
            title=profile_data['title'],
            company=profile_data['company'],
            location=profile_data['location']
        )
        
        # Send message
        result = linkedin_service.send_message(profile, message)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/run-campaign', methods=['POST'])
def run_campaign():
    """Run an automated outreach campaign"""
    data = request.json
    campaign_id = data.get('campaign_id')
    
    try:
        # Get campaign from database
        campaign = Campaign.query.get_or_404(campaign_id)
        
        # Prepare campaign configuration
        campaign_config = {
            'id': campaign.id,
            'target_audience': campaign.target_audience or {},
            'message_template': campaign.message_template or 'Hi {name}, I would like to connect with you.'
        }
        
        # Run the campaign
        results = linkedin_service.run_outreach_campaign(campaign_config)
        
        # Update campaign status
        campaign.status = 'running'
        campaign.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify(results)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/campaign-analytics/<int:campaign_id>', methods=['GET'])
def get_campaign_analytics(campaign_id):
    """Get analytics for a specific campaign"""
    try:
        analytics = linkedin_service.get_campaign_analytics(str(campaign_id))
        return jsonify(analytics)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/daily-limits', methods=['GET'])
def get_daily_limits():
    """Get current status of daily automation limits"""
    try:
        limits_status = linkedin_service.get_daily_limits_status()
        return jsonify(limits_status)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/personalize-message', methods=['POST'])
def personalize_message():
    """Personalize a message template with profile information"""
    data = request.json
    template = data.get('template')
    profile_data = data.get('profile')
    
    try:
        # Create LinkedInProfile object
        profile = LinkedInProfile(
            profile_url=profile_data['profile_url'],
            name=profile_data['name'],
            title=profile_data['title'],
            company=profile_data['company'],
            location=profile_data['location']
        )
        
        # Personalize message
        personalized = linkedin_service.personalize_message(template, profile)
        
        return jsonify({
            'original_template': template,
            'personalized_message': personalized,
            'profile': profile_data
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/bulk-connect', methods=['POST'])
def bulk_connect():
    """Send connection requests to multiple profiles"""
    data = request.json
    profiles_data = data.get('profiles', [])
    message_template = data.get('message_template', 'Hi {name}, I would like to connect with you.')
    user_id = data.get('user_id')
    
    results = {
        'total_profiles': len(profiles_data),
        'successful_requests': 0,
        'failed_requests': 0,
        'errors': [],
        'details': []
    }
    
    try:
        for profile_data in profiles_data:
            # Create LinkedInProfile object
            profile = LinkedInProfile(
                profile_url=profile_data['profile_url'],
                name=profile_data['name'],
                title=profile_data['title'],
                company=profile_data['company'],
                location=profile_data['location']
            )
            
            # Personalize message
            personalized_message = linkedin_service.personalize_message(message_template, profile)
            
            # Send connection request
            result = linkedin_service.send_connection_request(profile, personalized_message)
            
            if result['success']:
                results['successful_requests'] += 1
                
                # Save to database if user_id provided
                if user_id:
                    connection = Connection(
                        user_id=user_id,
                        linkedin_profile_url=profile.profile_url,
                        name=profile.name,
                        title=profile.title,
                        company=profile.company,
                        location=profile.location,
                        connection_status='pending',
                        notes=f"Bulk connection request sent: {personalized_message}"
                    )
                    db.session.add(connection)
            else:
                results['failed_requests'] += 1
                results['errors'].append(f"Failed to connect with {profile.name}: {result.get('error', 'Unknown error')}")
            
            results['details'].append({
                'profile': profile_data,
                'success': result['success'],
                'message': personalized_message,
                'error': result.get('error')
            })
        
        # Commit all database changes
        if user_id:
            db.session.commit()
        
        return jsonify(results)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/engagement-actions', methods=['POST'])
def perform_engagement_actions():
    """Perform engagement actions like viewing profiles, liking posts"""
    data = request.json
    profiles_data = data.get('profiles', [])
    actions = data.get('actions', ['view_profile'])  # view_profile, like_posts, comment
    
    results = {
        'total_profiles': len(profiles_data),
        'successful_actions': 0,
        'failed_actions': 0,
        'actions_performed': [],
        'errors': []
    }
    
    try:
        for profile_data in profiles_data:
            # Create LinkedInProfile object
            profile = LinkedInProfile(
                profile_url=profile_data['profile_url'],
                name=profile_data['name'],
                title=profile_data['title'],
                company=profile_data['company'],
                location=profile_data['location']
            )
            
            for action in actions:
                if action == 'view_profile':
                    result = linkedin_service.view_profile(profile)
                    
                    if result['success']:
                        results['successful_actions'] += 1
                        results['actions_performed'].append({
                            'profile': profile.name,
                            'action': 'profile_view',
                            'timestamp': result['timestamp']
                        })
                    else:
                        results['failed_actions'] += 1
                        results['errors'].append(f"Failed to view {profile.name}'s profile: {result.get('error', 'Unknown error')}")
        
        return jsonify(results)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

