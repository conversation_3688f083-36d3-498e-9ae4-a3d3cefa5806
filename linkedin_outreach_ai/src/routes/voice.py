import os
import tempfile
from flask import Blueprint, request, jsonify
from werkzeug.utils import secure_filename
from src.services.voice_service import VoiceService
from src.models.voice_profile import VoiceProfile
from src.database import db

voice_bp = Blueprint('voice', __name__)
voice_service = VoiceService()

ALLOWED_EXTENSIONS = {'wav', 'mp3', 'm4a', 'ogg', 'flac'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@voice_bp.route('/voice/transcribe', methods=['POST'])
def transcribe_audio():
    """Transcribe audio file to text"""
    try:
        if 'audio' not in request.files:
            return jsonify({'error': 'No audio file provided'}), 400
        
        file = request.files['audio']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file format. Supported formats: wav, mp3, m4a, ogg, flac'}), 400
        
        # Save file temporarily
        filename = secure_filename(file.filename)
        temp_dir = tempfile.mkdtemp()
        temp_path = os.path.join(temp_dir, filename)
        file.save(temp_path)
        
        try:
            # Transcribe audio
            transcript = voice_service.transcribe_audio(temp_path)
            
            return jsonify({
                'success': True,
                'transcript': transcript
            })
        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.remove(temp_path)
            os.rmdir(temp_dir)
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@voice_bp.route('/voice/voice-to-post', methods=['POST'])
def voice_to_post():
    """Convert voice note to LinkedIn post"""
    try:
        user_id = request.form.get('user_id', type=int)
        post_type = request.form.get('post_type', 'general')
        
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        if 'audio' not in request.files:
            return jsonify({'error': 'No audio file provided'}), 400
        
        file = request.files['audio']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file format'}), 400
        
        # Save file temporarily
        filename = secure_filename(file.filename)
        temp_dir = tempfile.mkdtemp()
        temp_path = os.path.join(temp_dir, filename)
        file.save(temp_path)
        
        try:
            # Convert voice to post
            result = voice_service.voice_to_post(user_id, temp_path, post_type)
            
            # Update voice profile if successful
            if result['success']:
                voice_service.update_voice_profile(user_id, result['transcript'])
            
            return jsonify(result)
        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.remove(temp_path)
            os.rmdir(temp_dir)
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@voice_bp.route('/voice/profile/<int:user_id>', methods=['GET'])
def get_voice_profile(user_id):
    """Get user's voice profile"""
    try:
        voice_profile = VoiceProfile.query.filter_by(user_id=user_id).first()
        
        if not voice_profile:
            return jsonify({
                'success': True,
                'profile': None,
                'message': 'No voice profile found. Upload voice notes to create one.'
            })
        
        return jsonify({
            'success': True,
            'profile': voice_profile.to_dict()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@voice_bp.route('/voice/profile/<int:user_id>', methods=['POST'])
def create_voice_profile(user_id):
    """Create or update voice profile"""
    try:
        data = request.get_json()
        
        # Get or create voice profile
        voice_profile = VoiceProfile.query.filter_by(user_id=user_id).first()
        if not voice_profile:
            voice_profile = VoiceProfile(user_id=user_id)
            db.session.add(voice_profile)
        
        # Update profile fields
        for key, value in data.items():
            if hasattr(voice_profile, key) and key not in ['id', 'user_id', 'created_at']:
                setattr(voice_profile, key, value)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'profile': voice_profile.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@voice_bp.route('/voice/profile/<int:user_id>/train', methods=['POST'])
def train_voice_profile(user_id):
    """Train voice profile with sample content"""
    try:
        data = request.get_json()
        sample_posts = data.get('sample_posts', [])
        
        if not sample_posts:
            return jsonify({'error': 'Sample posts are required for training'}), 400
        
        # Get or create voice profile
        voice_profile = VoiceProfile.query.filter_by(user_id=user_id).first()
        if not voice_profile:
            voice_profile = VoiceProfile(user_id=user_id)
            db.session.add(voice_profile)
        
        # Analyze sample posts to extract voice characteristics
        combined_text = ' '.join(sample_posts)
        analysis = voice_service.analyze_voice_characteristics(combined_text)
        
        if 'error' not in analysis:
            # Update voice profile with analysis
            voice_profile.tone = analysis.get('tone', voice_profile.tone)
            voice_profile.style = analysis.get('style', voice_profile.style)
            voice_profile.vocabulary_level = analysis.get('vocabulary_level', voice_profile.vocabulary_level)
            voice_profile.sample_posts = json.dumps(sample_posts)
            voice_profile.training_status = 'trained'
            voice_profile.last_trained = datetime.utcnow()
            
            db.session.commit()
            
            return jsonify({
                'success': True,
                'profile': voice_profile.to_dict(),
                'analysis': analysis
            })
        else:
            return jsonify({
                'success': False,
                'error': analysis['error']
            }), 400
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@voice_bp.route('/voice/analyze-text', methods=['POST'])
def analyze_text():
    """Analyze text for voice characteristics"""
    try:
        data = request.get_json()
        text = data.get('text')
        
        if not text:
            return jsonify({'error': 'Text is required'}), 400
        
        analysis = voice_service.analyze_voice_characteristics(text)
        return jsonify({
            'success': True,
            'analysis': analysis
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@voice_bp.route('/voice/generate-post', methods=['POST'])
def generate_post_with_voice():
    """Generate post using voice profile"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        transcript = data.get('transcript')
        post_type = data.get('post_type', 'general')
        
        if not user_id or not transcript:
            return jsonify({'error': 'User ID and transcript are required'}), 400
        
        # Get voice profile
        voice_profile = VoiceProfile.query.filter_by(user_id=user_id).first()
        
        # Generate post
        post_content = voice_service.generate_post_from_transcript(transcript, voice_profile, post_type)
        
        return jsonify({
            'success': True,
            'post_content': post_content,
            'post_type': post_type,
            'used_voice_profile': voice_profile is not None
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@voice_bp.route('/voice/profile/<int:user_id>/feedback', methods=['POST'])
def provide_feedback(user_id):
    """Provide feedback on generated content to improve voice profile"""
    try:
        data = request.get_json()
        rating = data.get('rating')  # 1-5 scale
        feedback_text = data.get('feedback')
        
        if not rating:
            return jsonify({'error': 'Rating is required'}), 400
        
        # Get voice profile
        voice_profile = VoiceProfile.query.filter_by(user_id=user_id).first()
        if not voice_profile:
            return jsonify({'error': 'Voice profile not found'}), 404
        
        # Update satisfaction score (simple moving average)
        if voice_profile.user_satisfaction:
            voice_profile.user_satisfaction = (voice_profile.user_satisfaction + rating) / 2
        else:
            voice_profile.user_satisfaction = rating
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Feedback recorded successfully',
            'updated_satisfaction': voice_profile.user_satisfaction
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

