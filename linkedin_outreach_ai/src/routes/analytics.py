from flask import Blueprint, jsonify, request
from datetime import datetime, timedelta
from sqlalchemy import func
from src.models.post import Post
from src.models.connection import Connection
from src.models.campaign import Campaign
from src.models.user import db

analytics_bp = Blueprint('analytics', __name__)

@analytics_bp.route('/analytics/dashboard/<int:user_id>', methods=['GET'])
def get_dashboard_data(user_id):
    # Get date range from query params
    days = int(request.args.get('days', 30))
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # Posts analytics
    total_posts = Post.query.filter_by(user_id=user_id).count()
    posts_in_period = Post.query.filter(
        Post.user_id == user_id,
        Post.created_at >= start_date
    ).count()
    
    # Connection analytics
    total_connections = Connection.query.filter_by(user_id=user_id).count()
    new_connections = Connection.query.filter(
        Connection.user_id == user_id,
        Connection.created_at >= start_date
    ).count()
    
    # Campaign analytics
    active_campaigns = Campaign.query.filter_by(
        user_id=user_id,
        status='active'
    ).count()
    
    # Engagement metrics (mock data for now)
    total_engagement = {
        'likes': 1250,
        'comments': 340,
        'shares': 89,
        'views': 15600
    }
    
    return jsonify({
        'period_days': days,
        'posts': {
            'total': total_posts,
            'in_period': posts_in_period,
            'growth_rate': round((posts_in_period / max(total_posts - posts_in_period, 1)) * 100, 1)
        },
        'connections': {
            'total': total_connections,
            'new': new_connections,
            'growth_rate': round((new_connections / max(total_connections - new_connections, 1)) * 100, 1)
        },
        'campaigns': {
            'active': active_campaigns
        },
        'engagement': total_engagement
    })

@analytics_bp.route('/analytics/posts/<int:user_id>', methods=['GET'])
def get_post_analytics(user_id):
    days = int(request.args.get('days', 30))
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # Get posts with engagement data
    posts = Post.query.filter(
        Post.user_id == user_id,
        Post.created_at >= start_date,
        Post.status == 'posted'
    ).order_by(Post.posted_time.desc()).all()
    
    post_analytics = []
    for post in posts:
        metrics = post.engagement_metrics or {}
        post_analytics.append({
            'id': post.id,
            'title': post.title,
            'content_preview': post.content[:100] + '...' if len(post.content) > 100 else post.content,
            'posted_time': post.posted_time.isoformat() if post.posted_time else None,
            'engagement': {
                'likes': metrics.get('likes', 0),
                'comments': metrics.get('comments', 0),
                'shares': metrics.get('shares', 0),
                'views': metrics.get('views', 0)
            },
            'engagement_rate': calculate_engagement_rate(metrics)
        })
    
    return jsonify({
        'posts': post_analytics,
        'summary': {
            'total_posts': len(post_analytics),
            'avg_engagement_rate': sum(p['engagement_rate'] for p in post_analytics) / len(post_analytics) if post_analytics else 0
        }
    })

@analytics_bp.route('/analytics/connections/<int:user_id>', methods=['GET'])
def get_connection_analytics(user_id):
    # Connection status distribution
    status_counts = db.session.query(
        Connection.connection_status,
        func.count(Connection.id)
    ).filter_by(user_id=user_id).group_by(Connection.connection_status).all()
    
    status_distribution = {status: count for status, count in status_counts}
    
    # Recent connections
    recent_connections = Connection.query.filter_by(user_id=user_id).order_by(
        Connection.created_at.desc()
    ).limit(10).all()
    
    # Connection growth over time (last 30 days)
    days = 30
    growth_data = []
    for i in range(days):
        date = datetime.utcnow() - timedelta(days=i)
        count = Connection.query.filter(
            Connection.user_id == user_id,
            func.date(Connection.created_at) == date.date()
        ).count()
        growth_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'new_connections': count
        })
    
    return jsonify({
        'status_distribution': status_distribution,
        'recent_connections': [conn.to_dict() for conn in recent_connections],
        'growth_data': list(reversed(growth_data))
    })

@analytics_bp.route('/analytics/campaigns/<int:user_id>', methods=['GET'])
def get_campaign_analytics(user_id):
    campaigns = Campaign.query.filter_by(user_id=user_id).all()
    
    campaign_analytics = []
    for campaign in campaigns:
        # Mock performance data
        performance = {
            'messages_sent': 45,
            'responses_received': 12,
            'connections_made': 8,
            'response_rate': 26.7
        }
        
        campaign_analytics.append({
            'id': campaign.id,
            'name': campaign.name,
            'status': campaign.status,
            'start_date': campaign.start_date.isoformat() if campaign.start_date else None,
            'performance': performance
        })
    
    return jsonify({
        'campaigns': campaign_analytics,
        'summary': {
            'total_campaigns': len(campaigns),
            'active_campaigns': len([c for c in campaigns if c.status == 'active']),
            'avg_response_rate': sum(c['performance']['response_rate'] for c in campaign_analytics) / len(campaign_analytics) if campaign_analytics else 0
        }
    })

def calculate_engagement_rate(metrics):
    """Calculate engagement rate based on metrics"""
    if not metrics:
        return 0.0
    
    total_engagement = metrics.get('likes', 0) + metrics.get('comments', 0) + metrics.get('shares', 0)
    views = metrics.get('views', 1)  # Avoid division by zero
    
    return round((total_engagement / views) * 100, 2) if views > 0 else 0.0

