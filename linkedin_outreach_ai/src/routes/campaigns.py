from flask import Blueprint, jsonify, request
from datetime import datetime
from src.models.campaign import Campaign, db

campaigns_bp = Blueprint('campaigns', __name__)

@campaigns_bp.route('/campaigns', methods=['GET'])
def get_campaigns():
    user_id = request.args.get('user_id')
    status = request.args.get('status')
    
    query = Campaign.query
    if user_id:
        query = query.filter_by(user_id=user_id)
    if status:
        query = query.filter_by(status=status)
    
    campaigns = query.order_by(Campaign.created_at.desc()).all()
    return jsonify([campaign.to_dict() for campaign in campaigns])

@campaigns_bp.route('/campaigns', methods=['POST'])
def create_campaign():
    data = request.json
    
    campaign = Campaign(
        user_id=data['user_id'],
        name=data['name'],
        description=data.get('description'),
        target_audience=data.get('target_audience'),
        message_template=data.get('message_template'),
        status=data.get('status', 'active'),
        start_date=datetime.fromisoformat(data['start_date']) if data.get('start_date') else None,
        end_date=datetime.fromisoformat(data['end_date']) if data.get('end_date') else None
    )
    
    db.session.add(campaign)
    db.session.commit()
    return jsonify(campaign.to_dict()), 201

@campaigns_bp.route('/campaigns/<int:campaign_id>', methods=['GET'])
def get_campaign(campaign_id):
    campaign = Campaign.query.get_or_404(campaign_id)
    return jsonify(campaign.to_dict())

@campaigns_bp.route('/campaigns/<int:campaign_id>', methods=['PUT'])
def update_campaign(campaign_id):
    campaign = Campaign.query.get_or_404(campaign_id)
    data = request.json
    
    campaign.name = data.get('name', campaign.name)
    campaign.description = data.get('description', campaign.description)
    campaign.target_audience = data.get('target_audience', campaign.target_audience)
    campaign.message_template = data.get('message_template', campaign.message_template)
    campaign.status = data.get('status', campaign.status)
    
    if data.get('start_date'):
        campaign.start_date = datetime.fromisoformat(data['start_date'])
    if data.get('end_date'):
        campaign.end_date = datetime.fromisoformat(data['end_date'])
    
    campaign.updated_at = datetime.utcnow()
    db.session.commit()
    return jsonify(campaign.to_dict())

@campaigns_bp.route('/campaigns/<int:campaign_id>', methods=['DELETE'])
def delete_campaign(campaign_id):
    campaign = Campaign.query.get_or_404(campaign_id)
    db.session.delete(campaign)
    db.session.commit()
    return '', 204

@campaigns_bp.route('/campaigns/<int:campaign_id>/start', methods=['POST'])
def start_campaign(campaign_id):
    campaign = Campaign.query.get_or_404(campaign_id)
    campaign.status = 'active'
    campaign.start_date = datetime.utcnow()
    campaign.updated_at = datetime.utcnow()
    
    db.session.commit()
    return jsonify(campaign.to_dict())

@campaigns_bp.route('/campaigns/<int:campaign_id>/pause', methods=['POST'])
def pause_campaign(campaign_id):
    campaign = Campaign.query.get_or_404(campaign_id)
    campaign.status = 'paused'
    campaign.updated_at = datetime.utcnow()
    
    db.session.commit()
    return jsonify(campaign.to_dict())

