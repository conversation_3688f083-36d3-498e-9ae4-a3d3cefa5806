from flask import Blueprint, request, jsonify
from src.services.lead_service import LeadService
from src.models.lead import Lead
from src.database import db

leads_bp = Blueprint('leads', __name__)
lead_service = LeadService()

@leads_bp.route('/leads', methods=['GET'])
def get_leads():
    """Get leads with filtering and pagination"""
    try:
        user_id = request.args.get('user_id', type=int)
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        # Get filter parameters
        filters = {
            'industry': request.args.get('industry'),
            'seniority_level': request.args.get('seniority_level'),
            'company_size': request.args.get('company_size'),
            'location': request.args.get('location'),
            'status': request.args.get('status'),
            'email_verified': request.args.get('email_verified', type=bool),
            'sort_by': request.args.get('sort_by', 'created_at'),
            'sort_order': request.args.get('sort_order', 'desc'),
            'page': request.args.get('page', 1, type=int),
            'per_page': request.args.get('per_page', 50, type=int)
        }
        
        # Remove None values
        filters = {k: v for k, v in filters.items() if v is not None}
        
        result = lead_service.search_leads(user_id, filters)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@leads_bp.route('/leads', methods=['POST'])
def create_lead():
    """Create a new lead with enrichment"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        result = lead_service.create_lead(user_id, data)
        
        if result['success']:
            return jsonify(result), 201
        else:
            return jsonify(result), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@leads_bp.route('/leads/<int:lead_id>', methods=['GET'])
def get_lead(lead_id):
    """Get a specific lead"""
    try:
        user_id = request.args.get('user_id', type=int)
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        lead = Lead.query.filter_by(id=lead_id, user_id=user_id).first()
        if not lead:
            return jsonify({'error': 'Lead not found'}), 404
        
        return jsonify({'success': True, 'lead': lead.to_dict()})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@leads_bp.route('/leads/<int:lead_id>', methods=['PUT'])
def update_lead(lead_id):
    """Update a lead"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        lead = Lead.query.filter_by(id=lead_id, user_id=user_id).first()
        if not lead:
            return jsonify({'error': 'Lead not found'}), 404
        
        # Update lead fields
        for key, value in data.items():
            if hasattr(lead, key) and key != 'id':
                setattr(lead, key, value)
        
        db.session.commit()
        
        return jsonify({'success': True, 'lead': lead.to_dict()})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@leads_bp.route('/leads/<int:lead_id>/status', methods=['PUT'])
def update_lead_status(lead_id):
    """Update lead status"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        status = data.get('status')
        notes = data.get('notes')
        
        if not user_id or not status:
            return jsonify({'error': 'User ID and status are required'}), 400
        
        result = lead_service.update_lead_status(user_id, lead_id, status, notes)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@leads_bp.route('/leads/bulk-import', methods=['POST'])
def bulk_import_leads():
    """Bulk import and enrich leads from CSV"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        csv_data = data.get('csv_data')
        
        if not user_id or not csv_data:
            return jsonify({'error': 'User ID and CSV data are required'}), 400
        
        result = lead_service.bulk_enrich_leads(user_id, csv_data)
        
        if result['success']:
            return jsonify(result), 201
        else:
            return jsonify(result), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@leads_bp.route('/leads/export', methods=['GET'])
def export_leads():
    """Export leads to CSV"""
    try:
        user_id = request.args.get('user_id', type=int)
        lead_ids = request.args.getlist('lead_ids', type=int)
        
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        csv_data = lead_service.export_leads_to_csv(user_id, lead_ids if lead_ids else None)
        
        return jsonify({
            'success': True,
            'csv_data': csv_data,
            'filename': f'leads_export_{user_id}.csv'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@leads_bp.route('/leads/find-email', methods=['POST'])
def find_email():
    """Find email address for a lead"""
    try:
        data = request.get_json()
        first_name = data.get('first_name')
        last_name = data.get('last_name')
        domain = data.get('domain')
        
        if not all([first_name, last_name, domain]):
            return jsonify({'error': 'First name, last name, and domain are required'}), 400
        
        result = lead_service.find_email_by_name_domain(first_name, last_name, domain)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@leads_bp.route('/leads/verify-email', methods=['POST'])
def verify_email():
    """Verify email address"""
    try:
        data = request.get_json()
        email = data.get('email')
        
        if not email:
            return jsonify({'error': 'Email is required'}), 400
        
        result = lead_service.verify_email(email)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@leads_bp.route('/leads/enrich-company', methods=['POST'])
def enrich_company():
    """Enrich company data"""
    try:
        data = request.get_json()
        domain = data.get('domain')
        
        if not domain:
            return jsonify({'error': 'Domain is required'}), 400
        
        result = lead_service.enrich_company_data(domain)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@leads_bp.route('/leads/stats', methods=['GET'])
def get_lead_stats():
    """Get lead statistics"""
    try:
        user_id = request.args.get('user_id', type=int)
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        # Get basic stats
        total_leads = Lead.query.filter_by(user_id=user_id).count()
        verified_emails = Lead.query.filter_by(user_id=user_id, email_verified=True).count()
        
        # Status breakdown
        status_stats = db.session.query(
            Lead.status, 
            db.func.count(Lead.id)
        ).filter_by(user_id=user_id).group_by(Lead.status).all()
        
        # Industry breakdown
        industry_stats = db.session.query(
            Lead.industry, 
            db.func.count(Lead.id)
        ).filter_by(user_id=user_id).group_by(Lead.industry).limit(10).all()
        
        return jsonify({
            'success': True,
            'stats': {
                'total_leads': total_leads,
                'verified_emails': verified_emails,
                'verification_rate': (verified_emails / total_leads * 100) if total_leads > 0 else 0,
                'status_breakdown': [{'status': status, 'count': count} for status, count in status_stats],
                'top_industries': [{'industry': industry, 'count': count} for industry, count in industry_stats]
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

