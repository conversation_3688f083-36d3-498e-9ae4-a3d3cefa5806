from flask import Blueprint, jsonify, request
from datetime import datetime
from src.models.post import Post, db

posts_bp = Blueprint('posts', __name__)

@posts_bp.route('/posts', methods=['GET'])
def get_posts():
    user_id = request.args.get('user_id')
    status = request.args.get('status')
    
    query = Post.query
    if user_id:
        query = query.filter_by(user_id=user_id)
    if status:
        query = query.filter_by(status=status)
    
    posts = query.order_by(Post.created_at.desc()).all()
    return jsonify([post.to_dict() for post in posts])

@posts_bp.route('/posts', methods=['POST'])
def create_post():
    data = request.json
    
    post = Post(
        user_id=data['user_id'],
        title=data.get('title'),
        content=data['content'],
        scheduled_time=datetime.fromisoformat(data['scheduled_time']) if data.get('scheduled_time') else None,
        status=data.get('status', 'draft')
    )
    
    db.session.add(post)
    db.session.commit()
    return jsonify(post.to_dict()), 201

@posts_bp.route('/posts/<int:post_id>', methods=['GET'])
def get_post(post_id):
    post = Post.query.get_or_404(post_id)
    return jsonify(post.to_dict())

@posts_bp.route('/posts/<int:post_id>', methods=['PUT'])
def update_post(post_id):
    post = Post.query.get_or_404(post_id)
    data = request.json
    
    post.title = data.get('title', post.title)
    post.content = data.get('content', post.content)
    post.status = data.get('status', post.status)
    
    if data.get('scheduled_time'):
        post.scheduled_time = datetime.fromisoformat(data['scheduled_time'])
    
    if data.get('engagement_metrics'):
        post.engagement_metrics = data['engagement_metrics']
    
    post.updated_at = datetime.utcnow()
    db.session.commit()
    return jsonify(post.to_dict())

@posts_bp.route('/posts/<int:post_id>', methods=['DELETE'])
def delete_post(post_id):
    post = Post.query.get_or_404(post_id)
    db.session.delete(post)
    db.session.commit()
    return '', 204

@posts_bp.route('/posts/<int:post_id>/schedule', methods=['POST'])
def schedule_post(post_id):
    post = Post.query.get_or_404(post_id)
    data = request.json
    
    post.scheduled_time = datetime.fromisoformat(data['scheduled_time'])
    post.status = 'scheduled'
    post.updated_at = datetime.utcnow()
    
    db.session.commit()
    return jsonify(post.to_dict())

@posts_bp.route('/posts/<int:post_id>/publish', methods=['POST'])
def publish_post(post_id):
    post = Post.query.get_or_404(post_id)
    
    # Here you would integrate with LinkedIn API to actually post
    # For now, we'll just mark it as posted
    post.posted_time = datetime.utcnow()
    post.status = 'posted'
    post.updated_at = datetime.utcnow()
    
    db.session.commit()
    return jsonify(post.to_dict())

