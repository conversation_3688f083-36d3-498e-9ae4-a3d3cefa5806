from flask import Blueprint, jsonify, request
from datetime import datetime
from src.models.connection import Connection, db

connections_bp = Blueprint('connections', __name__)

@connections_bp.route('/connections', methods=['GET'])
def get_connections():
    user_id = request.args.get('user_id')
    status = request.args.get('status')
    tag = request.args.get('tag')
    
    query = Connection.query
    if user_id:
        query = query.filter_by(user_id=user_id)
    if status:
        query = query.filter_by(connection_status=status)
    if tag:
        query = query.filter(Connection.tags.contains([tag]))
    
    connections = query.order_by(Connection.created_at.desc()).all()
    return jsonify([connection.to_dict() for connection in connections])

@connections_bp.route('/connections', methods=['POST'])
def create_connection():
    data = request.json
    
    connection = Connection(
        user_id=data['user_id'],
        linkedin_profile_url=data['linkedin_profile_url'],
        name=data.get('name'),
        title=data.get('title'),
        company=data.get('company'),
        location=data.get('location'),
        connection_status=data.get('connection_status', 'pending'),
        notes=data.get('notes'),
        tags=data.get('tags', [])
    )
    
    db.session.add(connection)
    db.session.commit()
    return jsonify(connection.to_dict()), 201

@connections_bp.route('/connections/<int:connection_id>', methods=['GET'])
def get_connection(connection_id):
    connection = Connection.query.get_or_404(connection_id)
    return jsonify(connection.to_dict())

@connections_bp.route('/connections/<int:connection_id>', methods=['PUT'])
def update_connection(connection_id):
    connection = Connection.query.get_or_404(connection_id)
    data = request.json
    
    connection.name = data.get('name', connection.name)
    connection.title = data.get('title', connection.title)
    connection.company = data.get('company', connection.company)
    connection.location = data.get('location', connection.location)
    connection.connection_status = data.get('connection_status', connection.connection_status)
    connection.notes = data.get('notes', connection.notes)
    connection.tags = data.get('tags', connection.tags)
    
    if data.get('connection_date'):
        connection.connection_date = datetime.fromisoformat(data['connection_date'])
    
    connection.updated_at = datetime.utcnow()
    db.session.commit()
    return jsonify(connection.to_dict())

@connections_bp.route('/connections/<int:connection_id>', methods=['DELETE'])
def delete_connection(connection_id):
    connection = Connection.query.get_or_404(connection_id)
    db.session.delete(connection)
    db.session.commit()
    return '', 204

@connections_bp.route('/connections/<int:connection_id>/interact', methods=['POST'])
def record_interaction(connection_id):
    connection = Connection.query.get_or_404(connection_id)
    connection.last_interaction = datetime.utcnow()
    connection.updated_at = datetime.utcnow()
    
    db.session.commit()
    return jsonify(connection.to_dict())

@connections_bp.route('/connections/bulk-import', methods=['POST'])
def bulk_import_connections():
    data = request.json
    connections_data = data.get('connections', [])
    user_id = data.get('user_id')
    
    created_connections = []
    for conn_data in connections_data:
        connection = Connection(
            user_id=user_id,
            linkedin_profile_url=conn_data['linkedin_profile_url'],
            name=conn_data.get('name'),
            title=conn_data.get('title'),
            company=conn_data.get('company'),
            location=conn_data.get('location'),
            connection_status=conn_data.get('connection_status', 'pending'),
            tags=conn_data.get('tags', [])
        )
        db.session.add(connection)
        created_connections.append(connection)
    
    db.session.commit()
    return jsonify([conn.to_dict() for conn in created_connections]), 201

