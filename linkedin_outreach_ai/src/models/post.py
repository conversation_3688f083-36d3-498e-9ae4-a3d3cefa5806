from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from src.models.user import db

class Post(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=True)
    content = db.Column(db.Text, nullable=False)
    scheduled_time = db.Column(db.DateTime, nullable=True)
    posted_time = db.Column(db.DateTime, nullable=True)
    status = db.Column(db.String(20), default='draft')  # draft, scheduled, posted, failed
    linkedin_post_id = db.Column(db.String(100), nullable=True)
    engagement_metrics = db.Column(db.JSON, nullable=True)  # likes, comments, shares, views
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Post {self.id}: {self.title or "Untitled"}>'

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'content': self.content,
            'scheduled_time': self.scheduled_time.isoformat() if self.scheduled_time else None,
            'posted_time': self.posted_time.isoformat() if self.posted_time else None,
            'status': self.status,
            'linkedin_post_id': self.linkedin_post_id,
            'engagement_metrics': self.engagement_metrics,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

