from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from src.models.user import db

class Connection(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>('user.id'), nullable=False)
    linkedin_profile_url = db.Column(db.String(500), nullable=False)
    name = db.Column(db.String(200), nullable=True)
    title = db.Column(db.String(300), nullable=True)
    company = db.Column(db.String(200), nullable=True)
    location = db.Column(db.String(200), nullable=True)
    connection_status = db.Column(db.String(20), default='pending')  # pending, connected, declined
    connection_date = db.Column(db.DateTime, nullable=True)
    last_interaction = db.Column(db.DateTime, nullable=True)
    notes = db.Column(db.Text, nullable=True)
    tags = db.Column(db.<PERSON>, nullable=True)  # array of tags for categorization
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Connection {self.id}: {self.name}>'

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'linkedin_profile_url': self.linkedin_profile_url,
            'name': self.name,
            'title': self.title,
            'company': self.company,
            'location': self.location,
            'connection_status': self.connection_status,
            'connection_date': self.connection_date.isoformat() if self.connection_date else None,
            'last_interaction': self.last_interaction.isoformat() if self.last_interaction else None,
            'notes': self.notes,
            'tags': self.tags,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

