import requests
import json
import csv
import io
from typing import Dict, List, Any, Optional
from src.models.lead import Lead
from src.database import db
from datetime import datetime

class LeadService:
    def __init__(self):
        # These would be configured via environment variables in production
        self.hunter_api_key = "your_hunter_api_key"
        self.clearbit_api_key = "your_clearbit_api_key"
        self.zerobounce_api_key = "your_zerobounce_api_key"
    
    def find_email_by_name_domain(self, first_name: str, last_name: str, domain: str) -> Dict[str, Any]:
        """
        Find email address using Hunter.io API
        """
        try:
            url = "https://api.hunter.io/v2/email-finder"
            params = {
                'domain': domain,
                'first_name': first_name,
                'last_name': last_name,
                'api_key': self.hunter_api_key
            }
            
            response = requests.get(url, params=params)
            data = response.json()
            
            if response.status_code == 200 and data.get('data'):
                email_data = data['data']
                return {
                    'success': True,
                    'email': email_data.get('email'),
                    'confidence': email_data.get('confidence'),
                    'sources': email_data.get('sources', [])
                }
            else:
                return {
                    'success': False,
                    'error': data.get('errors', [{}])[0].get('details', 'Email not found')
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def verify_email(self, email: str) -> Dict[str, Any]:
        """
        Verify email address using ZeroBounce API
        """
        try:
            url = "https://api.zerobounce.net/v2/validate"
            params = {
                'api_key': self.zerobounce_api_key,
                'email': email
            }
            
            response = requests.get(url, params=params)
            data = response.json()
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'status': data.get('status'),  # valid, invalid, catch-all, unknown, spamtrap, abuse, do_not_mail
                    'sub_status': data.get('sub_status'),
                    'confidence': data.get('confidence'),
                    'deliverability': data.get('status') in ['valid', 'catch-all']
                }
            else:
                return {
                    'success': False,
                    'error': 'Email verification failed'
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def enrich_company_data(self, domain: str) -> Dict[str, Any]:
        """
        Enrich company data using Clearbit API
        """
        try:
            url = f"https://company.clearbit.com/v2/companies/find"
            headers = {
                'Authorization': f'Bearer {self.clearbit_api_key}'
            }
            params = {
                'domain': domain
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'company_name': data.get('name'),
                    'industry': data.get('category', {}).get('industry'),
                    'size': data.get('metrics', {}).get('employees'),
                    'revenue': data.get('metrics', {}).get('annualRevenue'),
                    'funding': data.get('metrics', {}).get('raised'),
                    'location': data.get('geo', {}).get('city'),
                    'description': data.get('description')
                }
            else:
                return {
                    'success': False,
                    'error': 'Company data not found'
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def extract_linkedin_profile_data(self, linkedin_url: str) -> Dict[str, Any]:
        """
        Extract data from LinkedIn profile URL (simulated - would use LinkedIn API or scraping)
        """
        # This is a placeholder implementation
        # In production, this would integrate with LinkedIn API or use web scraping
        try:
            # Extract profile ID from URL
            profile_id = linkedin_url.split('/')[-1] if linkedin_url else None
            
            # Simulated data extraction
            return {
                'success': True,
                'profile_id': profile_id,
                'current_position': 'Software Engineer',
                'current_company': 'Tech Corp',
                'industry': 'Technology',
                'location': 'San Francisco, CA',
                'seniority_level': 'Individual Contributor'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_lead(self, user_id: int, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new lead with enriched data
        """
        try:
            # Create lead object
            lead = Lead(
                user_id=user_id,
                first_name=lead_data.get('first_name', ''),
                last_name=lead_data.get('last_name', ''),
                full_name=f"{lead_data.get('first_name', '')} {lead_data.get('last_name', '')}".strip(),
                linkedin_url=lead_data.get('linkedin_url'),
                source=lead_data.get('source', 'manual')
            )
            
            # Extract LinkedIn data if URL provided
            if lead_data.get('linkedin_url'):
                linkedin_data = self.extract_linkedin_profile_data(lead_data['linkedin_url'])
                if linkedin_data['success']:
                    lead.linkedin_profile_id = linkedin_data.get('profile_id')
                    lead.current_position = linkedin_data.get('current_position')
                    lead.current_company = linkedin_data.get('current_company')
                    lead.industry = linkedin_data.get('industry')
                    lead.location = linkedin_data.get('location')
                    lead.seniority_level = linkedin_data.get('seniority_level')
            
            # Find email if company domain provided
            if lead_data.get('company_domain') and lead.first_name and lead.last_name:
                email_result = self.find_email_by_name_domain(
                    lead.first_name, 
                    lead.last_name, 
                    lead_data['company_domain']
                )
                if email_result['success']:
                    lead.email = email_result['email']
                    lead.company_domain = lead_data['company_domain']
                    
                    # Verify email
                    verification_result = self.verify_email(lead.email)
                    if verification_result['success']:
                        lead.email_verified = verification_result['deliverability']
                        lead.email_verification_status = verification_result['status']
            
            # Enrich company data
            if lead.company_domain:
                company_data = self.enrich_company_data(lead.company_domain)
                if company_data['success']:
                    lead.company_size = str(company_data.get('size', ''))
                    lead.company_revenue = str(company_data.get('revenue', ''))
                    lead.company_funding = str(company_data.get('funding', ''))
                    lead.company_headcount = company_data.get('size')
            
            # Save to database
            db.session.add(lead)
            db.session.commit()
            
            return {
                'success': True,
                'lead': lead.to_dict()
            }
        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': str(e)
            }
    
    def bulk_enrich_leads(self, user_id: int, csv_data: str) -> Dict[str, Any]:
        """
        Bulk enrich leads from CSV data
        """
        try:
            # Parse CSV data
            csv_file = io.StringIO(csv_data)
            reader = csv.DictReader(csv_file)
            
            enriched_leads = []
            errors = []
            
            for row in reader:
                try:
                    # Create lead from CSV row
                    lead_data = {
                        'first_name': row.get('first_name', ''),
                        'last_name': row.get('last_name', ''),
                        'company_domain': row.get('company_domain', ''),
                        'linkedin_url': row.get('linkedin_url', ''),
                        'source': 'csv_import'
                    }
                    
                    result = self.create_lead(user_id, lead_data)
                    if result['success']:
                        enriched_leads.append(result['lead'])
                    else:
                        errors.append({
                            'row': row,
                            'error': result['error']
                        })
                except Exception as e:
                    errors.append({
                        'row': row,
                        'error': str(e)
                    })
            
            return {
                'success': True,
                'enriched_count': len(enriched_leads),
                'error_count': len(errors),
                'leads': enriched_leads,
                'errors': errors
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def search_leads(self, user_id: int, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Search leads with advanced filters
        """
        try:
            query = Lead.query.filter_by(user_id=user_id)
            
            # Apply filters
            if filters.get('industry'):
                query = query.filter(Lead.industry.ilike(f"%{filters['industry']}%"))
            
            if filters.get('seniority_level'):
                query = query.filter(Lead.seniority_level == filters['seniority_level'])
            
            if filters.get('company_size'):
                query = query.filter(Lead.company_size == filters['company_size'])
            
            if filters.get('location'):
                query = query.filter(Lead.location.ilike(f"%{filters['location']}%"))
            
            if filters.get('status'):
                query = query.filter(Lead.status == filters['status'])
            
            if filters.get('email_verified') is not None:
                query = query.filter(Lead.email_verified == filters['email_verified'])
            
            # Apply sorting
            sort_by = filters.get('sort_by', 'created_at')
            sort_order = filters.get('sort_order', 'desc')
            
            if hasattr(Lead, sort_by):
                if sort_order == 'desc':
                    query = query.order_by(getattr(Lead, sort_by).desc())
                else:
                    query = query.order_by(getattr(Lead, sort_by))
            
            # Apply pagination
            page = filters.get('page', 1)
            per_page = filters.get('per_page', 50)
            
            leads = query.paginate(
                page=page, 
                per_page=per_page, 
                error_out=False
            )
            
            return {
                'success': True,
                'leads': [lead.to_dict() for lead in leads.items],
                'total': leads.total,
                'pages': leads.pages,
                'current_page': leads.page
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def export_leads_to_csv(self, user_id: int, lead_ids: List[int] = None) -> str:
        """
        Export leads to CSV format
        """
        try:
            query = Lead.query.filter_by(user_id=user_id)
            
            if lead_ids:
                query = query.filter(Lead.id.in_(lead_ids))
            
            leads = query.all()
            
            # Create CSV
            output = io.StringIO()
            fieldnames = [
                'id', 'first_name', 'last_name', 'email', 'phone', 'linkedin_url',
                'current_position', 'current_company', 'industry', 'location',
                'company_size', 'company_revenue', 'seniority_level', 'status',
                'email_verified', 'created_at'
            ]
            
            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()
            
            for lead in leads:
                lead_dict = lead.to_dict()
                row = {field: lead_dict.get(field, '') for field in fieldnames}
                writer.writerow(row)
            
            return output.getvalue()
        except Exception as e:
            raise Exception(f"Error exporting leads: {str(e)}")
    
    def update_lead_status(self, user_id: int, lead_id: int, status: str, notes: str = None) -> Dict[str, Any]:
        """
        Update lead status and add notes
        """
        try:
            lead = Lead.query.filter_by(id=lead_id, user_id=user_id).first()
            if not lead:
                return {
                    'success': False,
                    'error': 'Lead not found'
                }
            
            lead.status = status
            lead.last_activity = datetime.utcnow()
            
            if notes:
                # Add notes to tags field (could be expanded to separate notes table)
                existing_tags = json.loads(lead.tags) if lead.tags else []
                existing_tags.append({
                    'type': 'note',
                    'content': notes,
                    'timestamp': datetime.utcnow().isoformat()
                })
                lead.tags = json.dumps(existing_tags)
            
            db.session.commit()
            
            return {
                'success': True,
                'lead': lead.to_dict()
            }
        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': str(e)
            }

