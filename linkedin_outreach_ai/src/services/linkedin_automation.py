import time
import random
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import requests
from dataclasses import dataclass

@dataclass
class LinkedInProfile:
    """Represents a LinkedIn profile for outreach"""
    profile_url: str
    name: str
    title: str
    company: str
    location: str
    mutual_connections: int = 0
    profile_views: int = 0

@dataclass
class OutreachMessage:
    """Represents an outreach message template"""
    template_id: str
    subject: str
    message: str
    personalization_fields: List[str]

class LinkedInAutomation:
    """
    LinkedIn automation service for connection requests and messaging.
    Note: This is a simulation for demonstration purposes.
    Real LinkedIn automation should comply with LinkedIn's terms of service.
    """
    
    def __init__(self):
        self.rate_limits = {
            'connection_requests_per_day': 100,
            'messages_per_day': 50,
            'profile_views_per_day': 200,
            'delay_between_actions': (30, 120)  # seconds
        }
        self.daily_counters = {
            'connection_requests': 0,
            'messages_sent': 0,
            'profiles_viewed': 0,
            'last_reset': datetime.now().date()
        }
    
    def reset_daily_counters(self):
        """Reset daily counters if it's a new day"""
        if self.daily_counters['last_reset'] < datetime.now().date():
            self.daily_counters = {
                'connection_requests': 0,
                'messages_sent': 0,
                'profiles_viewed': 0,
                'last_reset': datetime.now().date()
            }
    
    def can_perform_action(self, action_type: str) -> bool:
        """Check if we can perform a specific action based on rate limits"""
        self.reset_daily_counters()
        
        limits = {
            'connection_request': self.rate_limits['connection_requests_per_day'],
            'message': self.rate_limits['messages_per_day'],
            'profile_view': self.rate_limits['profile_views_per_day']
        }
        
        counters = {
            'connection_request': self.daily_counters['connection_requests'],
            'message': self.daily_counters['messages_sent'],
            'profile_view': self.daily_counters['profiles_viewed']
        }
        
        return counters.get(action_type, 0) < limits.get(action_type, 0)
    
    def add_random_delay(self):
        """Add random delay between actions to appear human-like"""
        min_delay, max_delay = self.rate_limits['delay_between_actions']
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def search_profiles(self, search_criteria: Dict) -> List[LinkedInProfile]:
        """
        Search for LinkedIn profiles based on criteria
        This is a simulation - real implementation would use LinkedIn API or web scraping
        """
        # Simulate search results
        mock_profiles = [
            LinkedInProfile(
                profile_url="https://linkedin.com/in/johndoe",
                name="John Doe",
                title="Software Engineer",
                company="Tech Corp",
                location="San Francisco, CA",
                mutual_connections=5,
                profile_views=150
            ),
            LinkedInProfile(
                profile_url="https://linkedin.com/in/janesmith",
                name="Jane Smith",
                title="Product Manager",
                company="Innovation Inc",
                location="New York, NY",
                mutual_connections=3,
                profile_views=200
            ),
            LinkedInProfile(
                profile_url="https://linkedin.com/in/mikejohnson",
                name="Mike Johnson",
                title="Marketing Director",
                company="Growth Co",
                location="Austin, TX",
                mutual_connections=8,
                profile_views=300
            )
        ]
        
        # Filter based on search criteria (simplified)
        filtered_profiles = []
        for profile in mock_profiles:
            if search_criteria.get('location') and search_criteria['location'].lower() in profile.location.lower():
                filtered_profiles.append(profile)
            elif search_criteria.get('title') and search_criteria['title'].lower() in profile.title.lower():
                filtered_profiles.append(profile)
            elif search_criteria.get('company') and search_criteria['company'].lower() in profile.company.lower():
                filtered_profiles.append(profile)
            elif not any([search_criteria.get('location'), search_criteria.get('title'), search_criteria.get('company')]):
                filtered_profiles.append(profile)
        
        return filtered_profiles[:10]  # Return max 10 results
    
    def send_connection_request(self, profile: LinkedInProfile, message: str = "") -> Dict:
        """
        Send a connection request to a LinkedIn profile
        """
        if not self.can_perform_action('connection_request'):
            return {
                'success': False,
                'error': 'Daily connection request limit reached',
                'profile': profile.profile_url
            }
        
        # Simulate sending connection request
        self.add_random_delay()
        
        # Simulate success/failure (90% success rate)
        success = random.random() > 0.1
        
        if success:
            self.daily_counters['connection_requests'] += 1
            return {
                'success': True,
                'profile': profile.profile_url,
                'message': message,
                'timestamp': datetime.now().isoformat(),
                'status': 'pending'
            }
        else:
            return {
                'success': False,
                'error': 'Connection request failed',
                'profile': profile.profile_url
            }
    
    def send_message(self, profile: LinkedInProfile, message: str) -> Dict:
        """
        Send a direct message to a connected LinkedIn profile
        """
        if not self.can_perform_action('message'):
            return {
                'success': False,
                'error': 'Daily message limit reached',
                'profile': profile.profile_url
            }
        
        # Simulate sending message
        self.add_random_delay()
        
        # Simulate success/failure (95% success rate)
        success = random.random() > 0.05
        
        if success:
            self.daily_counters['messages_sent'] += 1
            return {
                'success': True,
                'profile': profile.profile_url,
                'message': message,
                'timestamp': datetime.now().isoformat(),
                'message_id': f"msg_{random.randint(1000, 9999)}"
            }
        else:
            return {
                'success': False,
                'error': 'Message sending failed',
                'profile': profile.profile_url
            }
    
    def view_profile(self, profile: LinkedInProfile) -> Dict:
        """
        View a LinkedIn profile (for engagement)
        """
        if not self.can_perform_action('profile_view'):
            return {
                'success': False,
                'error': 'Daily profile view limit reached',
                'profile': profile.profile_url
            }
        
        # Simulate viewing profile
        self.add_random_delay()
        
        self.daily_counters['profiles_viewed'] += 1
        return {
            'success': True,
            'profile': profile.profile_url,
            'timestamp': datetime.now().isoformat(),
            'view_duration': random.randint(30, 180)  # seconds
        }
    
    def personalize_message(self, template: str, profile: LinkedInProfile) -> str:
        """
        Personalize a message template with profile information
        """
        personalized = template
        
        # Replace placeholders with profile data
        replacements = {
            '{name}': profile.name,
            '{first_name}': profile.name.split()[0],
            '{title}': profile.title,
            '{company}': profile.company,
            '{location}': profile.location
        }
        
        for placeholder, value in replacements.items():
            personalized = personalized.replace(placeholder, value)
        
        return personalized
    
    def run_outreach_campaign(self, campaign_config: Dict) -> Dict:
        """
        Run an automated outreach campaign
        """
        results = {
            'campaign_id': campaign_config.get('id', 'unknown'),
            'start_time': datetime.now().isoformat(),
            'profiles_processed': 0,
            'connection_requests_sent': 0,
            'messages_sent': 0,
            'errors': [],
            'success_rate': 0
        }
        
        # Search for profiles based on campaign criteria
        search_criteria = campaign_config.get('target_audience', {})
        profiles = self.search_profiles(search_criteria)
        
        message_template = campaign_config.get('message_template', 'Hi {name}, I would like to connect with you.')
        
        for profile in profiles:
            if not self.can_perform_action('connection_request'):
                results['errors'].append('Daily connection request limit reached')
                break
            
            # Personalize message
            personalized_message = self.personalize_message(message_template, profile)
            
            # Send connection request
            request_result = self.send_connection_request(profile, personalized_message)
            
            results['profiles_processed'] += 1
            
            if request_result['success']:
                results['connection_requests_sent'] += 1
            else:
                results['errors'].append(f"Failed to connect with {profile.name}: {request_result.get('error', 'Unknown error')}")
        
        # Calculate success rate
        if results['profiles_processed'] > 0:
            results['success_rate'] = (results['connection_requests_sent'] / results['profiles_processed']) * 100
        
        results['end_time'] = datetime.now().isoformat()
        
        return results
    
    def get_campaign_analytics(self, campaign_id: str) -> Dict:
        """
        Get analytics for a specific campaign
        """
        # This would typically fetch real data from database
        # For now, return mock analytics
        return {
            'campaign_id': campaign_id,
            'total_requests_sent': 45,
            'accepted_connections': 12,
            'pending_connections': 28,
            'declined_connections': 5,
            'acceptance_rate': 26.7,
            'response_rate': 15.6,
            'messages_sent': 12,
            'replies_received': 3,
            'meetings_scheduled': 1,
            'conversion_rate': 8.3
        }
    
    def get_daily_limits_status(self) -> Dict:
        """
        Get current status of daily limits
        """
        self.reset_daily_counters()
        
        return {
            'date': datetime.now().date().isoformat(),
            'connection_requests': {
                'used': self.daily_counters['connection_requests'],
                'limit': self.rate_limits['connection_requests_per_day'],
                'remaining': self.rate_limits['connection_requests_per_day'] - self.daily_counters['connection_requests']
            },
            'messages': {
                'used': self.daily_counters['messages_sent'],
                'limit': self.rate_limits['messages_per_day'],
                'remaining': self.rate_limits['messages_per_day'] - self.daily_counters['messages_sent']
            },
            'profile_views': {
                'used': self.daily_counters['profiles_viewed'],
                'limit': self.rate_limits['profile_views_per_day'],
                'remaining': self.rate_limits['profile_views_per_day'] - self.daily_counters['profiles_viewed']
            }
        }

