import os
import json
import openai
from typing import Dict, Any, Optional
from src.models.voice_profile import VoiceProfile
from src.database import db

class VoiceService:
    def __init__(self):
        self.client = openai.OpenAI()
    
    def transcribe_audio(self, audio_file_path: str) -> str:
        """
        Transcribe audio file to text using OpenAI Whisper
        """
        try:
            with open(audio_file_path, "rb") as audio_file:
                transcript = self.client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    response_format="text"
                )
            return transcript
        except Exception as e:
            raise Exception(f"Error transcribing audio: {str(e)}")
    
    def voice_to_post(self, user_id: int, audio_file_path: str, post_type: str = "general") -> Dict[str, Any]:
        """
        Convert voice note to LinkedIn post
        """
        try:
            # Transcribe audio
            transcript = self.transcribe_audio(audio_file_path)
            
            # Get user's voice profile
            voice_profile = VoiceProfile.query.filter_by(user_id=user_id).first()
            
            # Generate post from transcript
            post_content = self.generate_post_from_transcript(transcript, voice_profile, post_type)
            
            return {
                'success': True,
                'transcript': transcript,
                'post_content': post_content,
                'post_type': post_type
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_post_from_transcript(self, transcript: str, voice_profile: Optional[VoiceProfile], post_type: str) -> str:
        """
        Generate LinkedIn post from transcript using user's voice profile
        """
        # Base prompt
        prompt = f"Convert this voice note transcript into a professional LinkedIn post:\n\n{transcript}\n\n"
        
        # Add voice profile customization if available
        if voice_profile:
            prompt += f"Writing style: {voice_profile.tone or 'professional'}\n"
            prompt += f"Content style: {voice_profile.style or 'informative'}\n"
            prompt += f"Emoji usage: {voice_profile.emoji_usage or 'minimal'}\n"
            prompt += f"Hashtag preference: {voice_profile.hashtag_preference or 'few'}\n"
            
            if voice_profile.preferred_topics:
                topics = json.loads(voice_profile.preferred_topics)
                prompt += f"Preferred topics: {', '.join(topics)}\n"
        
        # Add post type specific instructions
        if post_type == "educational":
            prompt += "\nMake this an educational post with actionable insights."
        elif post_type == "personal_story":
            prompt += "\nFrame this as a personal story or experience."
        elif post_type == "thought_leadership":
            prompt += "\nPosition this as thought leadership content with industry insights."
        
        prompt += "\nKeep it engaging, authentic, and suitable for LinkedIn's professional audience."
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert LinkedIn content creator who helps users convert their ideas into engaging posts."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
        except Exception as e:
            raise Exception(f"Error generating post: {str(e)}")
    
    def analyze_voice_characteristics(self, transcript: str) -> Dict[str, Any]:
        """
        Analyze voice characteristics from transcript for voice profile training
        """
        prompt = f"""Analyze the following text and identify the speaker's communication characteristics:

{transcript}

Please provide analysis in the following format:
- Tone: (professional, casual, friendly, authoritative, etc.)
- Style: (educational, storytelling, data-driven, personal, etc.)
- Vocabulary Level: (simple, intermediate, advanced, technical)
- Sentence Structure: (short, medium, long, varied)
- Key Topics: (list main topics discussed)
- Communication Patterns: (any notable patterns or preferences)

Provide your analysis in JSON format."""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert in communication analysis and writing style identification."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.3
            )
            
            # Parse the JSON response
            analysis_text = response.choices[0].message.content.strip()
            # Remove markdown formatting if present
            if analysis_text.startswith("```json"):
                analysis_text = analysis_text[7:-3]
            elif analysis_text.startswith("```"):
                analysis_text = analysis_text[3:-3]
            
            return json.loads(analysis_text)
        except Exception as e:
            return {
                'error': f"Error analyzing voice characteristics: {str(e)}"
            }
    
    def update_voice_profile(self, user_id: int, transcript: str) -> bool:
        """
        Update user's voice profile based on new transcript
        """
        try:
            # Get or create voice profile
            voice_profile = VoiceProfile.query.filter_by(user_id=user_id).first()
            if not voice_profile:
                voice_profile = VoiceProfile(user_id=user_id)
                db.session.add(voice_profile)
            
            # Analyze voice characteristics
            analysis = self.analyze_voice_characteristics(transcript)
            
            if 'error' not in analysis:
                # Update voice profile with analysis
                if 'tone' in analysis:
                    voice_profile.tone = analysis['tone']
                if 'style' in analysis:
                    voice_profile.style = analysis['style']
                if 'vocabulary_level' in analysis:
                    voice_profile.vocabulary_level = analysis['vocabulary_level']
                
                # Add transcript to training data
                existing_transcripts = []
                if voice_profile.voice_notes_transcripts:
                    existing_transcripts = json.loads(voice_profile.voice_notes_transcripts)
                
                existing_transcripts.append({
                    'transcript': transcript,
                    'timestamp': datetime.utcnow().isoformat(),
                    'analysis': analysis
                })
                
                # Keep only last 50 transcripts to manage storage
                if len(existing_transcripts) > 50:
                    existing_transcripts = existing_transcripts[-50:]
                
                voice_profile.voice_notes_transcripts = json.dumps(existing_transcripts)
                voice_profile.training_status = 'trained'
                voice_profile.last_trained = datetime.utcnow()
                
                db.session.commit()
                return True
            
            return False
        except Exception as e:
            db.session.rollback()
            print(f"Error updating voice profile: {str(e)}")
            return False

