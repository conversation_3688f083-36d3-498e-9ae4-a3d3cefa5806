import requests
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from src.models.social_account import SocialAccount
from src.models.post import Post
from src.database import db

class SocialMediaService:
    def __init__(self):
        self.platform_configs = {
            'linkedin': {
                'api_base': 'https://api.linkedin.com/v2',
                'auth_url': 'https://www.linkedin.com/oauth/v2/authorization',
                'token_url': 'https://www.linkedin.com/oauth/v2/accessToken'
            },
            'facebook': {
                'api_base': 'https://graph.facebook.com/v18.0',
                'auth_url': 'https://www.facebook.com/v18.0/dialog/oauth',
                'token_url': 'https://graph.facebook.com/v18.0/oauth/access_token'
            },
            'instagram': {
                'api_base': 'https://graph.instagram.com',
                'auth_url': 'https://api.instagram.com/oauth/authorize',
                'token_url': 'https://api.instagram.com/oauth/access_token'
            },
            'twitter': {
                'api_base': 'https://api.twitter.com/2',
                'auth_url': 'https://twitter.com/i/oauth2/authorize',
                'token_url': 'https://api.twitter.com/2/oauth2/token'
            }
        }
    
    def connect_social_account(self, user_id: int, platform: str, auth_code: str) -> Dict[str, Any]:
        """
        Connect a social media account using OAuth
        """
        try:
            # Exchange auth code for access token
            token_data = self._exchange_auth_code(platform, auth_code)
            if not token_data['success']:
                return token_data
            
            # Get user profile information
            profile_data = self._get_user_profile(platform, token_data['access_token'])
            if not profile_data['success']:
                return profile_data
            
            # Create or update social account
            existing_account = SocialAccount.query.filter_by(
                user_id=user_id,
                platform=platform,
                platform_user_id=profile_data['user_id']
            ).first()
            
            if existing_account:
                # Update existing account
                existing_account.access_token = token_data['access_token']
                existing_account.refresh_token = token_data.get('refresh_token')
                existing_account.token_expires_at = token_data.get('expires_at')
                existing_account.connection_status = 'connected'
                existing_account.last_sync = datetime.utcnow()
                account = existing_account
            else:
                # Create new account
                account = SocialAccount(
                    user_id=user_id,
                    platform=platform,
                    platform_user_id=profile_data['user_id'],
                    username=profile_data.get('username'),
                    display_name=profile_data.get('display_name'),
                    profile_url=profile_data.get('profile_url'),
                    profile_image_url=profile_data.get('profile_image_url'),
                    access_token=token_data['access_token'],
                    refresh_token=token_data.get('refresh_token'),
                    token_expires_at=token_data.get('expires_at'),
                    followers_count=profile_data.get('followers_count', 0),
                    following_count=profile_data.get('following_count', 0),
                    posts_count=profile_data.get('posts_count', 0)
                )
                db.session.add(account)
            
            db.session.commit()
            
            return {
                'success': True,
                'account': account.to_dict()
            }
        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': str(e)
            }
    
    def _exchange_auth_code(self, platform: str, auth_code: str) -> Dict[str, Any]:
        """
        Exchange authorization code for access token
        """
        try:
            config = self.platform_configs.get(platform)
            if not config:
                return {'success': False, 'error': 'Unsupported platform'}
            
            # This is a simplified implementation
            # In production, you'd need proper OAuth flow with client credentials
            return {
                'success': True,
                'access_token': f'mock_token_{platform}_{auth_code}',
                'refresh_token': f'mock_refresh_{platform}_{auth_code}',
                'expires_at': datetime.utcnow() + timedelta(days=60)
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _get_user_profile(self, platform: str, access_token: str) -> Dict[str, Any]:
        """
        Get user profile information from social platform
        """
        try:
            # This is a mock implementation
            # In production, you'd make actual API calls to each platform
            mock_profiles = {
                'linkedin': {
                    'user_id': 'linkedin_123',
                    'username': 'john_doe',
                    'display_name': 'John Doe',
                    'profile_url': 'https://linkedin.com/in/johndoe',
                    'profile_image_url': 'https://media.licdn.com/profile.jpg',
                    'followers_count': 1500,
                    'following_count': 800,
                    'posts_count': 45
                },
                'facebook': {
                    'user_id': 'facebook_456',
                    'username': 'john.doe',
                    'display_name': 'John Doe',
                    'profile_url': 'https://facebook.com/john.doe',
                    'profile_image_url': 'https://graph.facebook.com/profile.jpg',
                    'followers_count': 2000,
                    'following_count': 1200,
                    'posts_count': 120
                }
            }
            
            profile = mock_profiles.get(platform, {})
            profile['success'] = True
            return profile
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def post_to_platform(self, account_id: int, content: str, media_urls: List[str] = None) -> Dict[str, Any]:
        """
        Post content to a specific social media platform
        """
        try:
            account = SocialAccount.query.get(account_id)
            if not account:
                return {'success': False, 'error': 'Account not found'}
            
            # Check if token is still valid
            if account.token_expires_at and account.token_expires_at < datetime.utcnow():
                return {'success': False, 'error': 'Token expired'}
            
            # Platform-specific posting logic
            if account.platform == 'linkedin':
                result = self._post_to_linkedin(account, content, media_urls)
            elif account.platform == 'facebook':
                result = self._post_to_facebook(account, content, media_urls)
            elif account.platform == 'instagram':
                result = self._post_to_instagram(account, content, media_urls)
            elif account.platform == 'twitter':
                result = self._post_to_twitter(account, content, media_urls)
            else:
                return {'success': False, 'error': 'Unsupported platform'}
            
            return result
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _post_to_linkedin(self, account: SocialAccount, content: str, media_urls: List[str] = None) -> Dict[str, Any]:
        """
        Post to LinkedIn
        """
        try:
            # Mock implementation - in production, use LinkedIn API
            post_id = f"linkedin_post_{datetime.utcnow().timestamp()}"
            
            return {
                'success': True,
                'platform_post_id': post_id,
                'post_url': f"https://linkedin.com/posts/{post_id}"
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _post_to_facebook(self, account: SocialAccount, content: str, media_urls: List[str] = None) -> Dict[str, Any]:
        """
        Post to Facebook
        """
        try:
            # Mock implementation - in production, use Facebook Graph API
            post_id = f"facebook_post_{datetime.utcnow().timestamp()}"
            
            return {
                'success': True,
                'platform_post_id': post_id,
                'post_url': f"https://facebook.com/posts/{post_id}"
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _post_to_instagram(self, account: SocialAccount, content: str, media_urls: List[str] = None) -> Dict[str, Any]:
        """
        Post to Instagram
        """
        try:
            # Instagram requires media for posts
            if not media_urls:
                return {'success': False, 'error': 'Instagram posts require media'}
            
            # Mock implementation - in production, use Instagram Basic Display API
            post_id = f"instagram_post_{datetime.utcnow().timestamp()}"
            
            return {
                'success': True,
                'platform_post_id': post_id,
                'post_url': f"https://instagram.com/p/{post_id}"
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _post_to_twitter(self, account: SocialAccount, content: str, media_urls: List[str] = None) -> Dict[str, Any]:
        """
        Post to Twitter/X
        """
        try:
            # Check character limit
            if len(content) > 280:
                return {'success': False, 'error': 'Content exceeds Twitter character limit'}
            
            # Mock implementation - in production, use Twitter API v2
            post_id = f"twitter_post_{datetime.utcnow().timestamp()}"
            
            return {
                'success': True,
                'platform_post_id': post_id,
                'post_url': f"https://twitter.com/status/{post_id}"
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def schedule_multi_platform_post(self, user_id: int, content: str, platforms: List[str], 
                                   scheduled_time: datetime, media_urls: List[str] = None) -> Dict[str, Any]:
        """
        Schedule a post across multiple platforms
        """
        try:
            # Get user's connected accounts for specified platforms
            accounts = SocialAccount.query.filter(
                SocialAccount.user_id == user_id,
                SocialAccount.platform.in_(platforms),
                SocialAccount.is_active == True
            ).all()
            
            if not accounts:
                return {'success': False, 'error': 'No connected accounts found for specified platforms'}
            
            scheduled_posts = []
            
            for account in accounts:
                # Customize content for each platform if needed
                platform_content = self._customize_content_for_platform(content, account.platform)
                
                # Create scheduled post record
                post = Post(
                    user_id=user_id,
                    content=platform_content,
                    scheduled_time=scheduled_time,
                    status='scheduled',
                    platform=account.platform,
                    social_account_id=account.id
                )
                
                if media_urls:
                    post.media_urls = json.dumps(media_urls)
                
                db.session.add(post)
                scheduled_posts.append(post)
            
            db.session.commit()
            
            return {
                'success': True,
                'scheduled_posts': [post.to_dict() for post in scheduled_posts]
            }
        except Exception as e:
            db.session.rollback()
            return {'success': False, 'error': str(e)}
    
    def _customize_content_for_platform(self, content: str, platform: str) -> str:
        """
        Customize content for specific platform requirements
        """
        if platform == 'twitter':
            # Truncate for Twitter character limit
            if len(content) > 280:
                return content[:277] + '...'
        elif platform == 'linkedin':
            # Add professional tone adjustments if needed
            pass
        elif platform == 'instagram':
            # Add hashtags or adjust for Instagram style
            pass
        
        return content
    
    def get_unified_inbox(self, user_id: int) -> Dict[str, Any]:
        """
        Get unified inbox with comments and messages from all platforms
        """
        try:
            # Get all connected accounts
            accounts = SocialAccount.query.filter_by(user_id=user_id, is_active=True).all()
            
            unified_messages = []
            
            for account in accounts:
                # Mock implementation - in production, fetch from each platform's API
                platform_messages = self._get_platform_messages(account)
                unified_messages.extend(platform_messages)
            
            # Sort by timestamp
            unified_messages.sort(key=lambda x: x['timestamp'], reverse=True)
            
            return {
                'success': True,
                'messages': unified_messages,
                'total_count': len(unified_messages)
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _get_platform_messages(self, account: SocialAccount) -> List[Dict[str, Any]]:
        """
        Get messages/comments from a specific platform
        """
        # Mock implementation
        return [
            {
                'id': f"{account.platform}_msg_1",
                'platform': account.platform,
                'type': 'comment',
                'content': f'Great post! Love your insights on {account.platform}.',
                'author': 'Jane Smith',
                'author_profile': f'https://{account.platform}.com/janesmith',
                'timestamp': datetime.utcnow() - timedelta(hours=2),
                'post_id': 'post_123',
                'is_read': False
            }
        ]
    
    def reply_to_message(self, account_id: int, message_id: str, reply_content: str) -> Dict[str, Any]:
        """
        Reply to a comment or message
        """
        try:
            account = SocialAccount.query.get(account_id)
            if not account:
                return {'success': False, 'error': 'Account not found'}
            
            # Platform-specific reply logic
            if account.platform == 'linkedin':
                result = self._reply_linkedin(account, message_id, reply_content)
            elif account.platform == 'facebook':
                result = self._reply_facebook(account, message_id, reply_content)
            else:
                return {'success': False, 'error': 'Reply not supported for this platform'}
            
            return result
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _reply_linkedin(self, account: SocialAccount, message_id: str, reply_content: str) -> Dict[str, Any]:
        """
        Reply to LinkedIn comment
        """
        # Mock implementation
        return {
            'success': True,
            'reply_id': f"linkedin_reply_{datetime.utcnow().timestamp()}"
        }
    
    def _reply_facebook(self, account: SocialAccount, message_id: str, reply_content: str) -> Dict[str, Any]:
        """
        Reply to Facebook comment
        """
        # Mock implementation
        return {
            'success': True,
            'reply_id': f"facebook_reply_{datetime.utcnow().timestamp()}"
        }
    
    def get_account_analytics(self, account_id: int, date_range: int = 30) -> Dict[str, Any]:
        """
        Get analytics for a specific social media account
        """
        try:
            account = SocialAccount.query.get(account_id)
            if not account:
                return {'success': False, 'error': 'Account not found'}
            
            # Mock analytics data
            analytics = {
                'success': True,
                'account_id': account_id,
                'platform': account.platform,
                'date_range': date_range,
                'metrics': {
                    'followers_growth': 25,
                    'posts_published': 12,
                    'total_engagement': 450,
                    'avg_engagement_rate': 3.2,
                    'reach': 15000,
                    'impressions': 25000,
                    'clicks': 320,
                    'shares': 45,
                    'comments': 78,
                    'likes': 327
                },
                'top_posts': [
                    {
                        'id': 'post_1',
                        'content': 'Top performing post content...',
                        'engagement': 125,
                        'reach': 5000
                    }
                ]
            }
            
            return analytics
        except Exception as e:
            return {'success': False, 'error': str(e)}

