<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Outreach AI - API Server</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .api-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .endpoint {
            background: rgba(0, 0, 0, 0.2);
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        .method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.8em;
            font-weight: bold;
            margin-right: 10px;
        }
        .get { background: #28a745; }
        .post { background: #007bff; }
        .put { background: #ffc107; color: #000; }
        .delete { background: #dc3545; }
        .feature {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid #00d4aa;
        }
        .status {
            text-align: center;
            padding: 20px;
            background: rgba(0, 212, 170, 0.2);
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 LinkedIn Outreach AI</h1>
        <div class="status">
            <h2>✅ API Server Running</h2>
            <p>Backend services are active and ready to handle requests</p>
        </div>

        <div class="api-section">
            <h3>🎯 Core Features</h3>
            <div class="feature">
                <strong>AI Content Generation</strong> - Create engaging LinkedIn posts with AI assistance
            </div>
            <div class="feature">
                <strong>Connection Management</strong> - Track and manage your LinkedIn network
            </div>
            <div class="feature">
                <strong>Campaign Automation</strong> - Automate outreach campaigns with personalized messages
            </div>
            <div class="feature">
                <strong>Analytics & Insights</strong> - Track performance and engagement metrics
            </div>
        </div>

        <div class="api-section">
            <h3>🔗 API Endpoints</h3>
            
            <h4>User Management</h4>
            <div class="endpoint">
                <span class="method get">GET</span> /api/users
            </div>
            <div class="endpoint">
                <span class="method post">POST</span> /api/users
            </div>

            <h4>Content & Posts</h4>
            <div class="endpoint">
                <span class="method get">GET</span> /api/posts
            </div>
            <div class="endpoint">
                <span class="method post">POST</span> /api/posts
            </div>

            <h4>AI Features</h4>
            <div class="endpoint">
                <span class="method post">POST</span> /api/ai/generate-content
            </div>
            <div class="endpoint">
                <span class="method get">GET</span> /api/ai/viral-inspiration
            </div>
            <div class="endpoint">
                <span class="method post">POST</span> /api/ai/hashtag-suggestions
            </div>

            <h4>Automation</h4>
            <div class="endpoint">
                <span class="method post">POST</span> /api/automation/search-profiles
            </div>
            <div class="endpoint">
                <span class="method post">POST</span> /api/automation/send-connection-request
            </div>
            <div class="endpoint">
                <span class="method post">POST</span> /api/automation/run-campaign
            </div>

            <h4>Analytics</h4>
            <div class="endpoint">
                <span class="method get">GET</span> /api/analytics/dashboard/{user_id}
            </div>
            <div class="endpoint">
                <span class="method get">GET</span> /api/analytics/posts/{user_id}
            </div>
        </div>

        <div class="api-section">
            <h3>🎨 Frontend Application</h3>
            <p>The React frontend is running separately on port 5173. Visit:</p>
            <div class="endpoint">
                <a href="http://localhost:5173" style="color: #00d4aa; text-decoration: none;">
                    http://localhost:5173
                </a>
            </div>
        </div>

        <div class="api-section">
            <h3>📚 Documentation</h3>
            <p>This LinkedIn Outreach AI application provides a complete solution for:</p>
            <ul>
                <li>AI-powered content creation and optimization</li>
                <li>Automated LinkedIn outreach campaigns</li>
                <li>Connection and network management</li>
                <li>Performance analytics and insights</li>
                <li>Viral content templates and inspiration</li>
            </ul>
        </div>
    </div>
</body>
</html>