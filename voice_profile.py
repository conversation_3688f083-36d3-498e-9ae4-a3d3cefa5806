from datetime import datetime
from src.database import db

class VoiceProfile(db.Model):
    __tablename__ = 'voice_profiles'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('users.id'), nullable=False, unique=True)
    
    # Voice Characteristics
    tone = db.Column(db.String(100))  # professional, casual, friendly, authoritative, etc.
    style = db.Column(db.String(100))  # educational, storytelling, data-driven, personal, etc.
    vocabulary_level = db.Column(db.String(50))  # simple, intermediate, advanced, technical
    
    # Writing Patterns
    avg_sentence_length = db.Column(db.Float)
    paragraph_structure = db.Column(db.String(100))  # short, medium, long
    emoji_usage = db.Column(db.String(50))  # none, minimal, moderate, frequent
    hashtag_preference = db.Column(db.String(50))  # none, few, moderate, many
    
    # Content Preferences
    preferred_topics = db.Column(db.Text)  # JSON array of topics
    industry_focus = db.Column(db.String(100))
    content_types = db.Column(db.Text)  # JSON array: thought_leadership, personal_story, tips, etc.
    
    # Training Data
    sample_posts = db.Column(db.Text)  # JSON array of user's past posts for training
    voice_notes_transcripts = db.Column(db.Text)  # JSON array of transcribed voice notes
    training_status = db.Column(db.String(50), default='not_trained')  # not_trained, training, trained
    
    # AI Model Configuration
    model_version = db.Column(db.String(50))
    custom_prompts = db.Column(db.Text)  # JSON object with custom prompts for different content types
    
    # Performance Metrics
    accuracy_score = db.Column(db.Float, default=0.0)  # How well AI matches user's voice
    user_satisfaction = db.Column(db.Float, default=0.0)  # User feedback on generated content
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_trained = db.Column(db.DateTime)
    
    # Relationships
    user = db.relationship('User', backref=db.backref('voice_profile', uselist=False))
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'tone': self.tone,
            'style': self.style,
            'vocabulary_level': self.vocabulary_level,
            'avg_sentence_length': self.avg_sentence_length,
            'paragraph_structure': self.paragraph_structure,
            'emoji_usage': self.emoji_usage,
            'hashtag_preference': self.hashtag_preference,
            'preferred_topics': self.preferred_topics,
            'industry_focus': self.industry_focus,
            'content_types': self.content_types,
            'training_status': self.training_status,
            'model_version': self.model_version,
            'accuracy_score': self.accuracy_score,
            'user_satisfaction': self.user_satisfaction,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'last_trained': self.last_trained.isoformat() if self.last_trained else None
        }
    
    @staticmethod
    def from_dict(data):
        profile = VoiceProfile()
        for key, value in data.items():
            if hasattr(profile, key):
                setattr(profile, key, value)
        return profile

