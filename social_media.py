from flask import Blueprint, request, jsonify
from datetime import datetime
from src.services.social_media_service import SocialMediaService
from src.models.social_account import SocialAccount
from src.database import db

social_media_bp = Blueprint('social_media', __name__)
social_media_service = SocialMediaService()

@social_media_bp.route('/social-accounts', methods=['GET'])
def get_social_accounts():
    """Get user's connected social media accounts"""
    try:
        user_id = request.args.get('user_id', type=int)
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        accounts = SocialAccount.query.filter_by(user_id=user_id).all()
        
        return jsonify({
            'success': True,
            'accounts': [account.to_dict() for account in accounts]
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@social_media_bp.route('/social-accounts/connect', methods=['POST'])
def connect_social_account():
    """Connect a new social media account"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        platform = data.get('platform')
        auth_code = data.get('auth_code')
        
        if not all([user_id, platform, auth_code]):
            return jsonify({'error': 'User ID, platform, and auth code are required'}), 400
        
        result = social_media_service.connect_social_account(user_id, platform, auth_code)
        
        if result['success']:
            return jsonify(result), 201
        else:
            return jsonify(result), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@social_media_bp.route('/social-accounts/<int:account_id>', methods=['DELETE'])
def disconnect_social_account(account_id):
    """Disconnect a social media account"""
    try:
        user_id = request.args.get('user_id', type=int)
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        account = SocialAccount.query.filter_by(id=account_id, user_id=user_id).first()
        if not account:
            return jsonify({'error': 'Account not found'}), 404
        
        db.session.delete(account)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Account disconnected successfully'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@social_media_bp.route('/social-accounts/<int:account_id>/toggle', methods=['PUT'])
def toggle_account_status(account_id):
    """Toggle account active status"""
    try:
        user_id = request.args.get('user_id', type=int)
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        account = SocialAccount.query.filter_by(id=account_id, user_id=user_id).first()
        if not account:
            return jsonify({'error': 'Account not found'}), 404
        
        account.is_active = not account.is_active
        db.session.commit()
        
        return jsonify({
            'success': True,
            'account': account.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@social_media_bp.route('/posts/multi-platform', methods=['POST'])
def schedule_multi_platform_post():
    """Schedule a post across multiple platforms"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        content = data.get('content')
        platforms = data.get('platforms', [])
        scheduled_time_str = data.get('scheduled_time')
        media_urls = data.get('media_urls', [])
        
        if not all([user_id, content, platforms]):
            return jsonify({'error': 'User ID, content, and platforms are required'}), 400
        
        # Parse scheduled time
        if scheduled_time_str:
            try:
                scheduled_time = datetime.fromisoformat(scheduled_time_str.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({'error': 'Invalid scheduled time format'}), 400
        else:
            scheduled_time = datetime.utcnow()
        
        result = social_media_service.schedule_multi_platform_post(
            user_id, content, platforms, scheduled_time, media_urls
        )
        
        if result['success']:
            return jsonify(result), 201
        else:
            return jsonify(result), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@social_media_bp.route('/posts/<int:account_id>/publish', methods=['POST'])
def publish_to_platform():
    """Publish content to a specific platform"""
    try:
        data = request.get_json()
        content = data.get('content')
        media_urls = data.get('media_urls', [])
        
        if not content:
            return jsonify({'error': 'Content is required'}), 400
        
        result = social_media_service.post_to_platform(account_id, content, media_urls)
        
        if result['success']:
            return jsonify(result), 201
        else:
            return jsonify(result), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@social_media_bp.route('/inbox/unified', methods=['GET'])
def get_unified_inbox():
    """Get unified inbox with messages from all platforms"""
    try:
        user_id = request.args.get('user_id', type=int)
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        result = social_media_service.get_unified_inbox(user_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@social_media_bp.route('/inbox/<int:account_id>/reply', methods=['POST'])
def reply_to_message():
    """Reply to a comment or message"""
    try:
        data = request.get_json()
        message_id = data.get('message_id')
        reply_content = data.get('reply_content')
        
        if not all([message_id, reply_content]):
            return jsonify({'error': 'Message ID and reply content are required'}), 400
        
        result = social_media_service.reply_to_message(account_id, message_id, reply_content)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@social_media_bp.route('/analytics/<int:account_id>', methods=['GET'])
def get_account_analytics():
    """Get analytics for a specific social media account"""
    try:
        date_range = request.args.get('date_range', 30, type=int)
        
        result = social_media_service.get_account_analytics(account_id, date_range)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@social_media_bp.route('/analytics/multi-platform', methods=['GET'])
def get_multi_platform_analytics():
    """Get combined analytics across all platforms"""
    try:
        user_id = request.args.get('user_id', type=int)
        date_range = request.args.get('date_range', 30, type=int)
        
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400
        
        # Get all user's accounts
        accounts = SocialAccount.query.filter_by(user_id=user_id, is_active=True).all()
        
        combined_analytics = {
            'success': True,
            'date_range': date_range,
            'platforms': [],
            'totals': {
                'followers_growth': 0,
                'posts_published': 0,
                'total_engagement': 0,
                'reach': 0,
                'impressions': 0
            }
        }
        
        for account in accounts:
            analytics = social_media_service.get_account_analytics(account.id, date_range)
            if analytics['success']:
                combined_analytics['platforms'].append(analytics)
                
                # Add to totals
                metrics = analytics['metrics']
                combined_analytics['totals']['followers_growth'] += metrics.get('followers_growth', 0)
                combined_analytics['totals']['posts_published'] += metrics.get('posts_published', 0)
                combined_analytics['totals']['total_engagement'] += metrics.get('total_engagement', 0)
                combined_analytics['totals']['reach'] += metrics.get('reach', 0)
                combined_analytics['totals']['impressions'] += metrics.get('impressions', 0)
        
        return jsonify(combined_analytics)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@social_media_bp.route('/platforms/supported', methods=['GET'])
def get_supported_platforms():
    """Get list of supported social media platforms"""
    try:
        platforms = [
            {
                'id': 'linkedin',
                'name': 'LinkedIn',
                'description': 'Professional networking platform',
                'features': ['posts', 'scheduling', 'analytics', 'messaging']
            },
            {
                'id': 'facebook',
                'name': 'Facebook',
                'description': 'Social networking platform',
                'features': ['posts', 'scheduling', 'analytics', 'comments']
            },
            {
                'id': 'instagram',
                'name': 'Instagram',
                'description': 'Photo and video sharing platform',
                'features': ['posts', 'scheduling', 'analytics']
            },
            {
                'id': 'twitter',
                'name': 'Twitter/X',
                'description': 'Microblogging platform',
                'features': ['posts', 'scheduling', 'analytics']
            }
        ]
        
        return jsonify({
            'success': True,
            'platforms': platforms
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@social_media_bp.route('/content/customize', methods=['POST'])
def customize_content_for_platforms():
    """Customize content for different platforms"""
    try:
        data = request.get_json()
        content = data.get('content')
        platforms = data.get('platforms', [])
        
        if not content or not platforms:
            return jsonify({'error': 'Content and platforms are required'}), 400
        
        customized_content = {}
        
        for platform in platforms:
            if platform == 'twitter':
                # Truncate for Twitter
                if len(content) > 280:
                    customized_content[platform] = content[:277] + '...'
                else:
                    customized_content[platform] = content
            elif platform == 'linkedin':
                # Add professional formatting
                customized_content[platform] = content
            elif platform == 'instagram':
                # Add hashtags suggestion
                customized_content[platform] = content + '\n\n#business #professional #growth'
            elif platform == 'facebook':
                # Keep original content
                customized_content[platform] = content
            else:
                customized_content[platform] = content
        
        return jsonify({
            'success': True,
            'customized_content': customized_content
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

