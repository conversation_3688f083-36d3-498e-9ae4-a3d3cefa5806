/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M21 17a9 9 0 0 0-15-6.7L3 13", key: "8mp6z9" }],
  ["path", { d: "M3 7v6h6", key: "1v2h90" }],
  ["circle", { cx: "12", cy: "17", r: "1", key: "1ixnty" }]
];
const UndoDot = createLucideIcon("undo-dot", __iconNode);

export { __iconNode, UndoDot as default };
//# sourceMappingURL=undo-dot.js.map
