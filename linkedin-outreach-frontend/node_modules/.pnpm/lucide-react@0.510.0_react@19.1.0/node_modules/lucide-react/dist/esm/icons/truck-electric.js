/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M14 19V7a2 2 0 0 0-2-2H9", key: "15peso" }],
  ["path", { d: "M15 19H9", key: "18q6dt" }],
  [
    "path",
    {
      d: "M19 19h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.62L18.3 9.38a1 1 0 0 0-.78-.38H14",
      key: "1dkp3j"
    }
  ],
  ["path", { d: "M2 13v5a1 1 0 0 0 1 1h2", key: "pkmmzz" }],
  [
    "path",
    { d: "M4 3 2.15 5.15a.495.495 0 0 0 .35.86h2.15a.47.47 0 0 1 .35.86L3 9.02", key: "1n26pd" }
  ],
  ["circle", { cx: "17", cy: "19", r: "2", key: "1nxcgd" }],
  ["circle", { cx: "7", cy: "19", r: "2", key: "gzo7y7" }]
];
const TruckElectric = createLucideIcon("truck-electric", __iconNode);

export { __iconNode, TruckElectric as default };
//# sourceMappingURL=truck-electric.js.map
