hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.2':
    '@babel/compat-data': private
  '@babel/core@7.27.1':
    '@babel/core': private
  '@babel/generator@7.27.1':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.1(@babel/core@7.27.1)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.1':
    '@babel/helpers': private
  '@babel/parser@7.27.2':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.27.1':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.1':
    '@babel/traverse': private
  '@babel/types@7.27.1':
    '@babel/types': private
  '@esbuild/aix-ppc64@0.25.4':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.4':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.4':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.4':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.4':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.4':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.4':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.4':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.4':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.4':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.4':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.4':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.4':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.4':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.4':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.4':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.4':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.4':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.4':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.4':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.4':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.4':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.4':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.4':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.4':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.26.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.2.2':
    '@eslint/config-helpers': public
  '@eslint/core@0.13.0':
    '@eslint/core': public
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.2.8':
    '@eslint/plugin-kit': public
  '@floating-ui/core@1.7.0':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.0':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@modelcontextprotocol/sdk@1.11.3':
    '@modelcontextprotocol/sdk': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.6(@types/react-dom@19.1.5(@types/react@19.1.4))(@types/react@19.1.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.6(@types/react-dom@19.1.5(@types/react@19.1.4))(@types/react@19.1.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.9(@types/react-dom@19.1.5(@types/react@19.1.4))(@types/react@19.1.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.6(@types/react-dom@19.1.5(@types/react@19.1.4))(@types/react@19.1.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.1(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.14(@types/react-dom@19.1.5(@types/react@19.1.4))(@types/react@19.1.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.6(@types/react-dom@19.1.5(@types/react@19.1.4))(@types/react@19.1.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.8(@types/react-dom@19.1.5(@types/react@19.1.4))(@types/react@19.1.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.5(@types/react@19.1.4))(@types/react@19.1.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.2(@types/react-dom@19.1.5(@types/react@19.1.4))(@types/react@19.1.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.9(@types/react-dom@19.1.5(@types/react@19.1.4))(@types/react@19.1.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-is-hydrated@0.1.0(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-use-is-hydrated': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.4)(react@19.1.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.2(@types/react-dom@19.1.5(@types/react@19.1.4))(@types/react@19.1.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@rollup/rollup-android-arm-eabi@4.40.2':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.40.2':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.40.2':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.40.2':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.40.2':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.40.2':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.40.2':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.40.2':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.40.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.40.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.40.2':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.40.2':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.40.2':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.40.2':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.40.2':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.40.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.40.2':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.40.2':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.40.2':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.40.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@tailwindcss/node@4.1.7':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.7':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.7':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.7':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.7':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.7':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.7':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.7':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.7':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.7':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.7':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.7':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.7':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.7':
    '@tailwindcss/oxide': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  accepts@2.0.0:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  balanced-match@1.0.2:
    balanced-match: private
  body-parser@2.2.0:
    body-parser: private
  brace-expansion@1.1.11:
    brace-expansion: private
  browserslist@4.24.5:
    browserslist: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001718:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chownr@3.0.0:
    chownr: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@1.0.2:
    cookie: private
  cors@2.8.5:
    cors: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-color@3.1.0:
    d3-color: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-format@3.1.0:
    d3-format: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  debug@4.4.1:
    debug: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  deep-is@0.1.4:
    deep-is: private
  depd@2.0.0:
    depd: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.155:
    electron-to-chromium: private
  embla-carousel-reactive-utils@8.6.0(embla-carousel@8.6.0):
    embla-carousel-reactive-utils: private
  embla-carousel@8.6.0:
    embla-carousel: private
  encodeurl@2.0.0:
    encodeurl: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  esbuild@0.25.4:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.3.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: public
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  eventemitter3@4.0.7:
    eventemitter3: private
  eventsource-parser@3.0.2:
    eventsource-parser: private
  eventsource@3.0.7:
    eventsource: private
  express-rate-limit@7.5.0(express@5.1.0):
    express-rate-limit: private
  express@5.1.0:
    express: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-equals@5.2.2:
    fast-equals: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fdir@6.4.4(picomatch@4.0.2):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  finalhandler@2.1.0:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  forwarded@0.2.0:
    forwarded: private
  fresh@2.0.0:
    fresh: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@6.0.2:
    glob-parent: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  hasown@2.0.2:
    hasown: private
  http-errors@2.0.0:
    http-errors: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inherits@2.0.4:
    inherits: private
  internmap@2.0.3:
    internmap: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-promise@4.0.0:
    is-promise: private
  isexe@2.0.0:
    isexe: private
  jiti@2.4.2:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@1.1.0:
    media-typer: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  motion-dom@12.15.0:
    motion-dom: private
  motion-utils@12.12.1:
    motion-utils: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@1.0.0:
    negotiator: private
  node-releases@2.0.19:
    node-releases: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-to-regexp@8.2.0:
    path-to-regexp: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pkce-challenge@5.0.0:
    pkce-challenge: private
  postcss@8.5.3:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prop-types@15.8.1:
    prop-types: private
  proxy-addr@2.0.7:
    proxy-addr: private
  punycode@2.3.1:
    punycode: private
  qs@6.14.0:
    qs: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  react-is@18.3.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  react-remove-scroll-bar@2.3.8(@types/react@19.1.4)(react@19.1.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.6.3(@types/react@19.1.4)(react@19.1.0):
    react-remove-scroll: private
  react-router@7.6.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-router: private
  react-smooth@4.0.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-smooth: private
  react-style-singleton@2.2.3(@types/react@19.1.4)(react@19.1.0):
    react-style-singleton: private
  react-transition-group@4.4.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-transition-group: private
  recharts-scale@0.4.5:
    recharts-scale: private
  resolve-from@4.0.0:
    resolve-from: private
  rollup@4.40.2:
    rollup: private
  router@2.2.0:
    router: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.26.0:
    scheduler: private
  semver@6.3.1:
    semver: private
  send@1.2.0:
    send: private
  serve-static@2.2.0:
    serve-static: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  source-map-js@1.2.1:
    source-map-js: private
  statuses@2.0.1:
    statuses: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  tapable@2.2.1:
    tapable: private
  tar@7.4.3:
    tar: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinyglobby@0.2.13:
    tinyglobby: private
  toidentifier@1.0.1:
    toidentifier: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-is@2.0.1:
    type-is: private
  unpipe@1.0.0:
    unpipe: private
  update-browserslist-db@1.1.3(browserslist@4.24.5):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(@types/react@19.1.4)(react@19.1.0):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@19.1.4)(react@19.1.0):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: private
  vary@1.1.2:
    vary: private
  victory-vendor@36.9.2:
    victory-vendor: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrappy@1.0.2:
    wrappy: private
  yallist@5.0.0:
    yallist: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zod-to-json-schema@3.24.5(zod@3.24.4):
    zod-to-json-schema: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.0
pendingBuilds: []
prunedAt: Sat, 09 Aug 2025 18:32:06 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.4'
  - '@esbuild/android-arm64@0.25.4'
  - '@esbuild/android-arm@0.25.4'
  - '@esbuild/android-x64@0.25.4'
  - '@esbuild/darwin-x64@0.25.4'
  - '@esbuild/freebsd-arm64@0.25.4'
  - '@esbuild/freebsd-x64@0.25.4'
  - '@esbuild/linux-arm64@0.25.4'
  - '@esbuild/linux-arm@0.25.4'
  - '@esbuild/linux-ia32@0.25.4'
  - '@esbuild/linux-loong64@0.25.4'
  - '@esbuild/linux-mips64el@0.25.4'
  - '@esbuild/linux-ppc64@0.25.4'
  - '@esbuild/linux-riscv64@0.25.4'
  - '@esbuild/linux-s390x@0.25.4'
  - '@esbuild/linux-x64@0.25.4'
  - '@esbuild/netbsd-arm64@0.25.4'
  - '@esbuild/netbsd-x64@0.25.4'
  - '@esbuild/openbsd-arm64@0.25.4'
  - '@esbuild/openbsd-x64@0.25.4'
  - '@esbuild/sunos-x64@0.25.4'
  - '@esbuild/win32-arm64@0.25.4'
  - '@esbuild/win32-ia32@0.25.4'
  - '@esbuild/win32-x64@0.25.4'
  - '@rollup/rollup-android-arm-eabi@4.40.2'
  - '@rollup/rollup-android-arm64@4.40.2'
  - '@rollup/rollup-darwin-x64@4.40.2'
  - '@rollup/rollup-freebsd-arm64@4.40.2'
  - '@rollup/rollup-freebsd-x64@4.40.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.40.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.40.2'
  - '@rollup/rollup-linux-arm64-gnu@4.40.2'
  - '@rollup/rollup-linux-arm64-musl@4.40.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.40.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.40.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.40.2'
  - '@rollup/rollup-linux-riscv64-musl@4.40.2'
  - '@rollup/rollup-linux-s390x-gnu@4.40.2'
  - '@rollup/rollup-linux-x64-gnu@4.40.2'
  - '@rollup/rollup-linux-x64-musl@4.40.2'
  - '@rollup/rollup-win32-arm64-msvc@4.40.2'
  - '@rollup/rollup-win32-ia32-msvc@4.40.2'
  - '@rollup/rollup-win32-x64-msvc@4.40.2'
  - '@tailwindcss/oxide-android-arm64@4.1.7'
  - '@tailwindcss/oxide-darwin-x64@4.1.7'
  - '@tailwindcss/oxide-freebsd-x64@4.1.7'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.7'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.7'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.7'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.7'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.7'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.7'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.7'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.7'
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
