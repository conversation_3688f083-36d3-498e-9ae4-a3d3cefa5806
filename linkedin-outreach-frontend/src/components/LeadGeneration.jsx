import React, { useState, useEffect } from 'react';
import { Search, Download, Upload, Mail, Phone, Building, MapPin, Filter, Plus } from 'lucide-react';

const LeadGeneration = () => {
  const [leads, setLeads] = useState([]);
  const [filters, setFilters] = useState({
    industry: '',
    seniority_level: '',
    location: '',
    email_verified: null
  });
  const [loading, setLoading] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [csvData, setCsvData] = useState('');

  const fetchLeads = async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        user_id: 1,
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v !== '' && v !== null))
      });
      
      const response = await fetch(`http://localhost:5000/api/leads?${queryParams}`);
      const data = await response.json();
      
      if (data.success) {
        setLeads(data.leads);
      }
    } catch (error) {
      console.error('Error fetching leads:', error);
    }
    setLoading(false);
  };

  const handleBulkImport = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/leads/bulk-import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_id: 1,
          csv_data: csvData
        })
      });
      
      const data = await response.json();
      if (data.success) {
        alert(`Successfully imported ${data.enriched_count} leads`);
        setShowImportModal(false);
        setCsvData('');
        fetchLeads();
      }
    } catch (error) {
      console.error('Error importing leads:', error);
    }
  };

  const exportLeads = async () => {
    try {
      const response = await fetch(`http://localhost:5000/api/leads/export?user_id=1`);
      const data = await response.json();
      
      if (data.success) {
        const blob = new Blob([data.csv_data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = data.filename;
        a.click();
      }
    } catch (error) {
      console.error('Error exporting leads:', error);
    }
  };

  useEffect(() => {
    fetchLeads();
  }, [filters]);

  return (
    <div className="p-6 bg-white">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Lead Generation</h2>
        <div className="flex gap-2">
          <button
            onClick={() => setShowImportModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Upload size={16} />
            Import CSV
          </button>
          <button
            onClick={exportLeads}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            <Download size={16} />
            Export
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <div className="flex items-center gap-2 mb-3">
          <Filter size={16} />
          <span className="font-medium">Filters</span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <select
            value={filters.industry}
            onChange={(e) => setFilters({...filters, industry: e.target.value})}
            className="px-3 py-2 border border-gray-300 rounded-lg"
          >
            <option value="">All Industries</option>
            <option value="Technology">Technology</option>
            <option value="Finance">Finance</option>
            <option value="Healthcare">Healthcare</option>
            <option value="Marketing">Marketing</option>
          </select>
          
          <select
            value={filters.seniority_level}
            onChange={(e) => setFilters({...filters, seniority_level: e.target.value})}
            className="px-3 py-2 border border-gray-300 rounded-lg"
          >
            <option value="">All Levels</option>
            <option value="CXO">CXO</option>
            <option value="VP">VP</option>
            <option value="Director">Director</option>
            <option value="Manager">Manager</option>
          </select>
          
          <input
            type="text"
            placeholder="Location"
            value={filters.location}
            onChange={(e) => setFilters({...filters, location: e.target.value})}
            className="px-3 py-2 border border-gray-300 rounded-lg"
          />
          
          <select
            value={filters.email_verified === null ? '' : filters.email_verified.toString()}
            onChange={(e) => setFilters({...filters, email_verified: e.target.value === '' ? null : e.target.value === 'true'})}
            className="px-3 py-2 border border-gray-300 rounded-lg"
          >
            <option value="">All Emails</option>
            <option value="true">Verified Only</option>
            <option value="false">Unverified</option>
          </select>
        </div>
      </div>

      {/* Leads Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Company
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Position
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact Info
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="5" className="px-6 py-4 text-center text-gray-500">
                    Loading leads...
                  </td>
                </tr>
              ) : leads.length === 0 ? (
                <tr>
                  <td colSpan="5" className="px-6 py-4 text-center text-gray-500">
                    No leads found. Try adjusting your filters or import some leads.
                  </td>
                </tr>
              ) : (
                leads.map((lead) => (
                  <tr key={lead.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {lead.full_name}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center gap-1">
                          <MapPin size={12} />
                          {lead.location || 'Unknown'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 flex items-center gap-1">
                          <Building size={12} />
                          {lead.current_company || 'Unknown'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {lead.industry || 'Unknown Industry'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {lead.current_position || 'Unknown'}
                      </div>
                      <div className="text-sm text-gray-500">
                        {lead.seniority_level || 'Unknown Level'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        {lead.email && (
                          <div className="flex items-center gap-1 text-sm">
                            <Mail size={12} />
                            <span className={lead.email_verified ? 'text-green-600' : 'text-gray-600'}>
                              {lead.email}
                            </span>
                            {lead.email_verified && (
                              <span className="text-xs bg-green-100 text-green-800 px-1 rounded">
                                ✓
                              </span>
                            )}
                          </div>
                        )}
                        {lead.phone && (
                          <div className="flex items-center gap-1 text-sm text-gray-600">
                            <Phone size={12} />
                            {lead.phone}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        lead.status === 'new' ? 'bg-blue-100 text-blue-800' :
                        lead.status === 'contacted' ? 'bg-yellow-100 text-yellow-800' :
                        lead.status === 'responded' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {lead.status}
                      </span>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Import Modal */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Import Leads from CSV</h3>
            <p className="text-sm text-gray-600 mb-4">
              Upload a CSV file with columns: first_name, last_name, company_domain, linkedin_url
            </p>
            <textarea
              value={csvData}
              onChange={(e) => setCsvData(e.target.value)}
              placeholder="Paste CSV data here..."
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg resize-none"
            />
            <div className="flex justify-end gap-2 mt-4">
              <button
                onClick={() => setShowImportModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleBulkImport}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Import
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeadGeneration;

