import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  MessageSquare, 
  UserPlus, 
  Building, 
  MapPin,
  Calendar,
  MoreHorizontal,
  Download,
  Upload
} from 'lucide-react';

const Connections = () => {
  const [connections, setConnections] = useState([]);
  const [filteredConnections, setFilteredConnections] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [tagFilter, setTagFilter] = useState('all');

  useEffect(() => {
    // Mock data for connections
    const mockConnections = [
      {
        id: 1,
        name: "Sarah Johnson",
        title: "Senior Marketing Manager",
        company: "TechCorp Inc.",
        location: "San Francisco, CA",
        linkedin_profile_url: "https://linkedin.com/in/sarahjohnson",
        connection_status: "connected",
        connection_date: "2025-01-10T00:00:00Z",
        last_interaction: "2025-01-14T00:00:00Z",
        tags: ["marketing", "tech", "lead"],
        notes: "Met at TechConf 2024. Interested in AI marketing tools."
      },
      {
        id: 2,
        name: "Michael Chen",
        title: "Product Manager",
        company: "StartupXYZ",
        location: "New York, NY",
        linkedin_profile_url: "https://linkedin.com/in/michaelchen",
        connection_status: "pending",
        connection_date: null,
        last_interaction: null,
        tags: ["product", "startup"],
        notes: "Reached out about product collaboration opportunities."
      },
      {
        id: 3,
        name: "Emily Rodriguez",
        title: "VP of Sales",
        company: "Enterprise Solutions",
        location: "Chicago, IL",
        linkedin_profile_url: "https://linkedin.com/in/emilyrodriguez",
        connection_status: "connected",
        connection_date: "2025-01-08T00:00:00Z",
        last_interaction: "2025-01-12T00:00:00Z",
        tags: ["sales", "enterprise", "decision-maker"],
        notes: "Potential client for our enterprise package."
      },
      {
        id: 4,
        name: "David Kim",
        title: "Software Engineer",
        company: "Google",
        location: "Mountain View, CA",
        linkedin_profile_url: "https://linkedin.com/in/davidkim",
        connection_status: "connected",
        connection_date: "2025-01-05T00:00:00Z",
        last_interaction: "2025-01-15T00:00:00Z",
        tags: ["engineering", "tech", "google"],
        notes: "Former colleague. Great for technical insights."
      },
      {
        id: 5,
        name: "Lisa Wang",
        title: "HR Director",
        company: "People First Co.",
        location: "Austin, TX",
        linkedin_profile_url: "https://linkedin.com/in/lisawang",
        connection_status: "declined",
        connection_date: null,
        last_interaction: null,
        tags: ["hr", "recruiting"],
        notes: "Connection request declined."
      }
    ];

    setConnections(mockConnections);
    setFilteredConnections(mockConnections);
  }, []);

  useEffect(() => {
    let filtered = connections;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(conn => 
        conn.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conn.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conn.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(conn => conn.connection_status === statusFilter);
    }

    // Filter by tag
    if (tagFilter !== 'all') {
      filtered = filtered.filter(conn => conn.tags.includes(tagFilter));
    }

    setFilteredConnections(filtered);
  }, [connections, searchTerm, statusFilter, tagFilter]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'declined': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getInitials = (name) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const allTags = [...new Set(connections.flatMap(conn => conn.tags))];

  const connectionStats = {
    total: connections.length,
    connected: connections.filter(c => c.connection_status === 'connected').length,
    pending: connections.filter(c => c.connection_status === 'pending').length,
    declined: connections.filter(c => c.connection_status === 'declined').length
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Connections</h1>
          <p className="text-gray-600 mt-1">Manage your LinkedIn network and outreach</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            Add Connection
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Connections</p>
                <p className="text-2xl font-bold">{connectionStats.total}</p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Connected</p>
                <p className="text-2xl font-bold text-green-600">{connectionStats.connected}</p>
              </div>
              <UserPlus className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{connectionStats.pending}</p>
              </div>
              <Calendar className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Declined</p>
                <p className="text-2xl font-bold text-red-600">{connectionStats.declined}</p>
              </div>
              <MoreHorizontal className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search connections..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="connected">Connected</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="declined">Declined</SelectItem>
                </SelectContent>
              </Select>
              <Select value={tagFilter} onValueChange={setTagFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Tags" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tags</SelectItem>
                  {allTags.map(tag => (
                    <SelectItem key={tag} value={tag}>{tag}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Connections List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Network ({filteredConnections.length})</CardTitle>
          <CardDescription>
            Manage your LinkedIn connections and track interactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredConnections.map((connection) => (
              <div key={connection.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <Avatar className="w-12 h-12">
                      <AvatarFallback className="bg-blue-100 text-blue-600">
                        {getInitials(connection.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold text-lg">{connection.name}</h3>
                        <Badge className={getStatusColor(connection.connection_status)}>
                          {connection.connection_status}
                        </Badge>
                      </div>
                      <p className="text-gray-600 mb-1">{connection.title}</p>
                      <div className="flex items-center text-sm text-gray-500 space-x-4 mb-2">
                        <div className="flex items-center">
                          <Building className="w-4 h-4 mr-1" />
                          {connection.company}
                        </div>
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1" />
                          {connection.location}
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-1 mb-2">
                        {connection.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      {connection.notes && (
                        <p className="text-sm text-gray-600 italic">{connection.notes}</p>
                      )}
                      <div className="flex items-center text-xs text-gray-500 mt-2 space-x-4">
                        <span>Connected: {formatDate(connection.connection_date)}</span>
                        <span>Last interaction: {formatDate(connection.last_interaction)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      <MessageSquare className="w-4 h-4 mr-1" />
                      Message
                    </Button>
                    <Button size="sm" variant="outline">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Connections;

