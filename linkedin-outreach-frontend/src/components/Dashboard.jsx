import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { TrendingUp, Users, MessageSquare, Calendar, Plus, Eye, Heart, Share2, MessageCircle } from 'lucide-react';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    posts: { total: 0, in_period: 0, growth_rate: 0 },
    connections: { total: 0, new: 0, growth_rate: 0 },
    campaigns: { active: 0 },
    engagement: { likes: 0, comments: 0, shares: 0, views: 0 }
  });

  const [recentPosts, setRecentPosts] = useState([]);

  useEffect(() => {
    // Mock data for demonstration
    setDashboardData({
      posts: { total: 45, in_period: 12, growth_rate: 36.4 },
      connections: { total: 1250, new: 89, growth_rate: 7.7 },
      campaigns: { active: 3 },
      engagement: { likes: 1250, comments: 340, shares: 89, views: 15600 }
    });

    setRecentPosts([
      {
        id: 1,
        title: "5 LinkedIn Growth Strategies That Actually Work",
        content: "After analyzing 10,000+ LinkedIn posts, here are the strategies that consistently drive engagement...",
        posted_time: "2025-01-15T10:30:00Z",
        engagement: { likes: 156, comments: 23, shares: 12, views: 2340 }
      },
      {
        id: 2,
        title: "The Future of AI in Business",
        content: "Artificial Intelligence is reshaping how we work. Here's what every professional needs to know...",
        posted_time: "2025-01-14T14:15:00Z",
        engagement: { likes: 203, comments: 45, shares: 18, views: 3120 }
      },
      {
        id: 3,
        title: "Building Authentic Connections on LinkedIn",
        content: "Networking isn't about collecting contacts. It's about building meaningful relationships...",
        posted_time: "2025-01-13T09:45:00Z",
        engagement: { likes: 89, comments: 12, shares: 6, views: 1890 }
      }
    ]);
  }, []);

  const engagementData = [
    { name: 'Likes', value: dashboardData.engagement.likes, color: '#3b82f6' },
    { name: 'Comments', value: dashboardData.engagement.comments, color: '#10b981' },
    { name: 'Shares', value: dashboardData.engagement.shares, color: '#f59e0b' },
    { name: 'Views', value: dashboardData.engagement.views, color: '#8b5cf6' }
  ];

  const weeklyGrowthData = [
    { day: 'Mon', posts: 2, connections: 12, engagement: 145 },
    { day: 'Tue', posts: 1, connections: 8, engagement: 89 },
    { day: 'Wed', posts: 3, connections: 15, engagement: 234 },
    { day: 'Thu', posts: 2, connections: 11, engagement: 167 },
    { day: 'Fri', posts: 1, connections: 6, engagement: 98 },
    { day: 'Sat', posts: 0, connections: 3, engagement: 45 },
    { day: 'Sun', posts: 1, connections: 5, engagement: 78 }
  ];

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateEngagementRate = (engagement) => {
    const total = engagement.likes + engagement.comments + engagement.shares;
    return ((total / engagement.views) * 100).toFixed(1);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Welcome back! Here's your LinkedIn performance overview.</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Plus className="w-4 h-4 mr-2" />
          Create Post
        </Button>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.posts.total}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+{dashboardData.posts.in_period}</span> this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connections</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.connections.total.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+{dashboardData.connections.new}</span> new this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.campaigns.active}</div>
            <p className="text-xs text-muted-foreground">
              Running campaigns
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.engagement.views.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Weekly Growth Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Weekly Activity</CardTitle>
            <CardDescription>Your posting and engagement activity this week</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={weeklyGrowthData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="posts" fill="#3b82f6" name="Posts" />
                <Bar dataKey="connections" fill="#10b981" name="Connections" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Engagement Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Engagement Distribution</CardTitle>
            <CardDescription>Breakdown of your content engagement</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={engagementData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {engagementData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Posts */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Posts</CardTitle>
          <CardDescription>Your latest LinkedIn posts and their performance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentPosts.map((post) => (
              <div key={post.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-lg">{post.title}</h3>
                  <Badge variant="outline">{formatDate(post.posted_time)}</Badge>
                </div>
                <p className="text-gray-600 mb-3 line-clamp-2">{post.content}</p>
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Heart className="w-4 h-4" />
                    <span>{post.engagement.likes}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MessageCircle className="w-4 h-4" />
                    <span>{post.engagement.comments}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Share2 className="w-4 h-4" />
                    <span>{post.engagement.shares}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Eye className="w-4 h-4" />
                    <span>{post.engagement.views}</span>
                  </div>
                  <div className="ml-auto">
                    <Badge variant="secondary">
                      {calculateEngagementRate(post.engagement)}% engagement
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;

