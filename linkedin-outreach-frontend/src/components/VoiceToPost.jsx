import React, { useState, useRef } from 'react';
import { Mi<PERSON>, MicOff, Play, Pause, Upload, Wand2, Settings } from 'lucide-react';

const VoiceToPost = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState(null);
  const [transcript, setTranscript] = useState('');
  const [generatedPost, setGeneratedPost] = useState('');
  const [postType, setPostType] = useState('general');
  const [loading, setLoading] = useState(false);
  const [voiceProfile, setVoiceProfile] = useState(null);
  
  const mediaRecorderRef = useRef(null);
  const audioRef = useRef(null);
  const fileInputRef = useRef(null);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      const chunks = [];

      mediaRecorder.ondataavailable = (event) => {
        chunks.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/wav' });
        setAudioBlob(blob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Error accessing microphone. Please check permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setAudioBlob(file);
    }
  };

  const convertVoiceToPost = async () => {
    if (!audioBlob) return;

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.wav');
      formData.append('user_id', '1');
      formData.append('post_type', postType);

      const response = await fetch('http://localhost:5000/api/voice/voice-to-post', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      if (data.success) {
        setTranscript(data.transcript);
        setGeneratedPost(data.post_content);
      } else {
        alert('Error converting voice to post: ' + data.error);
      }
    } catch (error) {
      console.error('Error converting voice to post:', error);
      alert('Error converting voice to post');
    }
    setLoading(false);
  };

  const fetchVoiceProfile = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/voice/profile/1');
      const data = await response.json();
      if (data.success && data.profile) {
        setVoiceProfile(data.profile);
      }
    } catch (error) {
      console.error('Error fetching voice profile:', error);
    }
  };

  React.useEffect(() => {
    fetchVoiceProfile();
  }, []);

  return (
    <div className="p-6 bg-white">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Voice to Post</h2>
          <div className="flex items-center gap-2">
            <Settings size={16} className="text-gray-500" />
            <span className="text-sm text-gray-600">
              Voice Profile: {voiceProfile ?

