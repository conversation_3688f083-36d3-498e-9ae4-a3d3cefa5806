import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Sparkles, RefreshCw, Calendar, Send, Lightbulb, TrendingUp, Hash } from 'lucide-react';

const ContentCreator = () => {
  const [topic, setTopic] = useState('');
  const [tone, setTone] = useState('professional');
  const [format, setFormat] = useState('general');
  const [generatedContent, setGeneratedContent] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [hashtags, setHashtags] = useState([]);
  const [engagementScore, setEngagementScore] = useState(null);

  const viralTemplates = [
    {
      hook: "Here's what I learned after 5 years of building my personal brand:",
      format: "lesson_learned",
      engagement_score: 8.5
    },
    {
      hook: "Unpopular opinion: Most LinkedIn advice is wrong.",
      format: "controversial_take",
      engagement_score: 9.2
    },
    {
      hook: "I made a mistake that cost me 6 months of growth. Here's what I learned:",
      format: "failure_story",
      engagement_score: 8.8
    },
    {
      hook: "3 things I wish I knew before starting my LinkedIn journey:",
      format: "advice_list",
      engagement_score: 7.9
    },
    {
      hook: "This simple change increased my engagement by 300%:",
      format: "success_story",
      engagement_score: 8.3
    }
  ];

  const generateContent = async () => {
    if (!topic.trim()) return;
    
    setIsGenerating(true);
    
    // Simulate API call
    setTimeout(() => {
      const sampleContent = `🚀 ${topic} is transforming the way we work!

After spending months researching this topic, here are the key insights I've discovered:

✅ The landscape is evolving rapidly
✅ Early adopters are seeing significant benefits
✅ There are still challenges to overcome

The most important lesson? Start small, learn fast, and iterate.

What's your experience with ${topic}? Share your thoughts below! 👇

#${topic.replace(/\s+/g, '')} #Innovation #Growth #Leadership #Technology`;

      setGeneratedContent(sampleContent);
      setHashtags(['#Innovation', '#Growth', '#Leadership', '#Technology', `#${topic.replace(/\s+/g, '')}`]);
      setEngagementScore({
        score: 7.8,
        predicted_range: "78-117 interactions",
        recommendations: [
          "Add a question to encourage comments",
          "Consider adding more specific hashtags",
          "Include a personal story for better engagement"
        ]
      });
      setIsGenerating(false);
    }, 2000);
  };

  const improveContent = async () => {
    if (!generatedContent.trim()) return;
    
    setIsGenerating(true);
    
    // Simulate content improvement
    setTimeout(() => {
      const improvedContent = generatedContent + "\n\nP.S. I'd love to hear about your biggest challenge with " + topic + ". Drop a comment and let's discuss! 💬";
      setGeneratedContent(improvedContent);
      setEngagementScore(prev => ({
        ...prev,
        score: Math.min(10, prev.score + 0.5),
        recommendations: prev.recommendations.filter(r => !r.includes("question"))
      }));
      setIsGenerating(false);
    }, 1500);
  };

  const schedulePost = () => {
    // Simulate scheduling
    alert('Post scheduled successfully!');
  };

  const publishNow = () => {
    // Simulate publishing
    alert('Post published successfully!');
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Content Creator</h1>
          <p className="text-gray-600 mt-1">Create engaging LinkedIn posts with AI assistance</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Content Generation Panel */}
        <div className="lg:col-span-2 space-y-6">
          {/* Input Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Sparkles className="w-5 h-5 mr-2 text-blue-600" />
                AI Content Generator
              </CardTitle>
              <CardDescription>
                Enter a topic and let AI create engaging LinkedIn content for you
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <Input
                    placeholder="Enter your topic (e.g., AI in business, remote work, leadership)"
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div>
                  <Select value={tone} onValueChange={setTone}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select tone" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="casual">Casual</SelectItem>
                      <SelectItem value="inspirational">Inspirational</SelectItem>
                      <SelectItem value="educational">Educational</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Select value={format} onValueChange={setFormat}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General Post</SelectItem>
                    <SelectItem value="lesson_learned">Lesson Learned</SelectItem>
                    <SelectItem value="controversial_take">Controversial Take</SelectItem>
                    <SelectItem value="advice_list">Advice List</SelectItem>
                    <SelectItem value="success_story">Success Story</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button 
                onClick={generateContent} 
                disabled={!topic.trim() || isGenerating}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 mr-2" />
                    Generate Content
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Generated Content */}
          {generatedContent && (
            <Card>
              <CardHeader>
                <CardTitle>Generated Content</CardTitle>
                <CardDescription>
                  Review and edit your AI-generated LinkedIn post
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  value={generatedContent}
                  onChange={(e) => setGeneratedContent(e.target.value)}
                  rows={12}
                  className="w-full"
                />
                
                {hashtags.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center">
                      <Hash className="w-4 h-4 mr-1" />
                      Suggested Hashtags
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {hashtags.map((tag, index) => (
                        <Badge key={index} variant="secondary">{tag}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex space-x-2">
                  <Button 
                    onClick={improveContent}
                    variant="outline"
                    disabled={isGenerating}
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Improve Content
                  </Button>
                  <Button 
                    onClick={schedulePost}
                    variant="outline"
                  >
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule
                  </Button>
                  <Button 
                    onClick={publishNow}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Publish Now
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Engagement Prediction */}
          {engagementScore && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
                  Engagement Prediction
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-4">
                  <div className="text-3xl font-bold text-green-600">
                    {engagementScore.score}/10
                  </div>
                  <p className="text-sm text-gray-600">{engagementScore.predicted_range}</p>
                </div>
                
                {engagementScore.recommendations.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Recommendations:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {engagementScore.recommendations.map((rec, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-600 mr-1">•</span>
                          {rec}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Viral Templates */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="w-5 h-5 mr-2 text-yellow-600" />
                Viral Templates
              </CardTitle>
              <CardDescription>
                High-performing post formats to inspire your content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {viralTemplates.map((template, index) => (
                  <div 
                    key={index}
                    className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => setTopic(template.hook)}
                  >
                    <p className="text-sm font-medium mb-1">{template.hook}</p>
                    <div className="flex justify-between items-center">
                      <Badge variant="outline" className="text-xs">
                        {template.format.replace('_', ' ')}
                      </Badge>
                      <span className="text-xs text-green-600 font-medium">
                        {template.engagement_score}/10
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Content Tips */}
          <Card>
            <CardHeader>
              <CardTitle>Content Tips</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex items-start">
                  <span className="text-blue-600 mr-2">💡</span>
                  <span>Posts with questions get 50% more comments</span>
                </div>
                <div className="flex items-start">
                  <span className="text-blue-600 mr-2">📊</span>
                  <span>Optimal post length is 150-300 words</span>
                </div>
                <div className="flex items-start">
                  <span className="text-blue-600 mr-2">🎯</span>
                  <span>Use 3-5 relevant hashtags for best reach</span>
                </div>
                <div className="flex items-start">
                  <span className="text-blue-600 mr-2">⏰</span>
                  <span>Best posting times: 8-10 AM and 12-2 PM</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ContentCreator;

