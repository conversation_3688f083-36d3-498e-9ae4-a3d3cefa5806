from datetime import datetime
from src.database import db

class Lead(db.Model):
    __tablename__ = 'leads'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    
    # Basic Information
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    
    # Contact Information
    email = db.Column(db.String(255))
    phone = db.Column(db.String(50))
    email_verified = db.Column(db.<PERSON>an, default=False)
    email_verification_status = db.Column(db.String(50))  # valid, invalid, risky, unknown
    
    # LinkedIn Information
    linkedin_url = db.Column(db.String(500))
    linkedin_profile_id = db.Column(db.String(100))
    current_position = db.Column(db.String(200))
    current_company = db.Column(db.String(200))
    industry = db.Column(db.String(100))
    location = db.Column(db.String(200))
    
    # Company Information (Enriched Data)
    company_domain = db.Column(db.String(200))
    company_size = db.Column(db.String(50))
    company_revenue = db.Column(db.String(50))
    company_funding = db.Column(db.String(50))
    company_headcount = db.Column(db.Integer)
    
    # Professional Information
    seniority_level = db.Column(db.String(50))  # CXO, VP, Director, Manager, Individual Contributor
    job_function = db.Column(db.String(100))  # Sales, Marketing, Engineering, etc.
    years_experience = db.Column(db.Integer)
    
    # Lead Source and Status
    source = db.Column(db.String(100))  # linkedin_search, post_engagement, sales_navigator, etc.
    status = db.Column(db.String(50), default='new')  # new, contacted, responded, qualified, converted
    tags = db.Column(db.Text)  # JSON array of tags
    
    # Engagement Data
    last_activity = db.Column(db.DateTime)
    engagement_score = db.Column(db.Float, default=0.0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref=db.backref('leads', lazy=True))
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'email': self.email,
            'phone': self.phone,
            'email_verified': self.email_verified,
            'email_verification_status': self.email_verification_status,
            'linkedin_url': self.linkedin_url,
            'linkedin_profile_id': self.linkedin_profile_id,
            'current_position': self.current_position,
            'current_company': self.current_company,
            'industry': self.industry,
            'location': self.location,
            'company_domain': self.company_domain,
            'company_size': self.company_size,
            'company_revenue': self.company_revenue,
            'company_funding': self.company_funding,
            'company_headcount': self.company_headcount,
            'seniority_level': self.seniority_level,
            'job_function': self.job_function,
            'years_experience': self.years_experience,
            'source': self.source,
            'status': self.status,
            'tags': self.tags,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'engagement_score': self.engagement_score,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @staticmethod
    def from_dict(data):
        lead = Lead()
        for key, value in data.items():
            if hasattr(lead, key):
                setattr(lead, key, value)
        return lead

