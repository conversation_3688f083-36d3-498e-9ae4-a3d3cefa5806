from datetime import datetime
from src.database import db

class SocialAccount(db.Model):
    __tablename__ = 'social_accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    
    # Account Information
    platform = db.Column(db.String(50), nullable=False)  # linkedin, facebook, instagram, twitter, etc.
    platform_user_id = db.Column(db.String(100), nullable=False)
    username = db.Column(db.String(100))
    display_name = db.Column(db.String(200))
    profile_url = db.Column(db.String(500))
    profile_image_url = db.Column(db.String(500))
    
    # Authentication
    access_token = db.Column(db.Text)
    refresh_token = db.Column(db.Text)
    token_expires_at = db.Column(db.DateTime)
    
    # Account Status
    is_active = db.Column(db.Boolean, default=True)
    is_verified = db.Column(db.Boolean, default=False)
    connection_status = db.Column(db.String(50), default='connected')  # connected, expired, error
    
    # Account Metrics
    followers_count = db.Column(db.Integer, default=0)
    following_count = db.Column(db.Integer, default=0)
    posts_count = db.Column(db.Integer, default=0)
    
    # Settings
    auto_post_enabled = db.Column(db.Boolean, default=True)
    engagement_enabled = db.Column(db.Boolean, default=True)
    analytics_enabled = db.Column(db.Boolean, default=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_sync = db.Column(db.DateTime)
    
    # Relationships
    user = db.relationship('User', backref=db.backref('social_accounts', lazy=True))
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'platform': self.platform,
            'platform_user_id': self.platform_user_id,
            'username': self.username,
            'display_name': self.display_name,
            'profile_url': self.profile_url,
            'profile_image_url': self.profile_image_url,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'connection_status': self.connection_status,
            'followers_count': self.followers_count,
            'following_count': self.following_count,
            'posts_count': self.posts_count,
            'auto_post_enabled': self.auto_post_enabled,
            'engagement_enabled': self.engagement_enabled,
            'analytics_enabled': self.analytics_enabled,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'last_sync': self.last_sync.isoformat() if self.last_sync else None
        }
    
    @staticmethod
    def from_dict(data):
        account = SocialAccount()
        for key, value in data.items():
            if hasattr(account, key) and key not in ['access_token', 'refresh_token']:
                setattr(account, key, value)
        return account

