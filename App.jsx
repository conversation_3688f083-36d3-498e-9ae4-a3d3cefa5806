import { useState } from 'react'
import Sidebar from './components/Sidebar'import Dashboard from './components/Dashboard'
import ContentCreator from './components/ContentCreator'
import Sidebar from './components/Sidebar'
import Connections from './components/Connections'
import LeadGeneration from './components/LeadGeneration'
import VoiceToPost from './components/VoiceToPost'nst [activeTab, setActiveTab] = useState('dashboard')

  const renderContent = () => {
    switch (activeTab)       case 'dashboard':
        return <Dashboard />
      case 'content':
        return <ContentCreator />
      case 'connections':
        return <Connections />
      case 'leads':
        return <LeadGeneration />
      case 'voice':
        return <VoiceToPost />
      default:
        return <Dashboard />iv className="p-6"><h1 className="text-3xl font-bold">Campaigns</h1><p className="text-gray-600 mt-2">Campaign management coming soon...</p></div>
      case 'analytics':
        return <div className="p-6"><h1 className="text-3xl font-bold">Analytics</h1><p className="text-gray-600 mt-2">Advanced analytics coming soon...</p></div>
      case 'scheduler':
        return <div className="p-6"><h1 className="text-3xl font-bold">Scheduler</h1><p className="text-gray-600 mt-2">Post scheduler coming soon...</p></div>
      default:
        return <Dashboard />
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar activeTab={activeTab} setActiveTab={setActiveTab} />
      <div className="flex-1 overflow-auto">
        {renderContent()}
      </div>
    </div>
  )
}

export default App
